#
# Automatically generated file. DO NOT EDIT.
# Espressif IoT Development Framework (ESP-IDF) Configuration cmake include file
#
set(CONFIG_SOC_MPU_MIN_REGION_SIZE "0x20000000")
set(CONFIG_SOC_MPU_REGIONS_MAX_NUM "8")
set(CONFIG_SOC_ADC_SUPPORTED "y")
set(CONFIG_SOC_UART_SUPPORTED "y")
set(CONFIG_SOC_PCNT_SUPPORTED "y")
set(CONFIG_SOC_WIFI_SUPPORTED "y")
set(CONFIG_SOC_TWAI_SUPPORTED "y")
set(CONFIG_SOC_GDMA_SUPPORTED "y")
set(CONFIG_SOC_GPTIMER_SUPPORTED "y")
set(CONFIG_SOC_LCDCAM_SUPPORTED "y")
set(CONFIG_SOC_MCPWM_SUPPORTED "y")
set(CONFIG_SOC_DEDICATED_GPIO_SUPPORTED "y")
set(CONFIG_SOC_CACHE_SUPPORT_WRAP "y")
set(CONFIG_SOC_ULP_SUPPORTED "y")
set(CONFIG_SOC_ULP_FSM_SUPPORTED "y")
set(CONFIG_SOC_RISCV_COPROC_SUPPORTED "y")
set(CONFIG_SOC_BT_SUPPORTED "y")
set(CONFIG_SOC_USB_OTG_SUPPORTED "y")
set(CONFIG_SOC_USB_SERIAL_JTAG_SUPPORTED "y")
set(CONFIG_SOC_CCOMP_TIMER_SUPPORTED "y")
set(CONFIG_SOC_ASYNC_MEMCPY_SUPPORTED "y")
set(CONFIG_SOC_SUPPORTS_SECURE_DL_MODE "y")
set(CONFIG_SOC_EFUSE_KEY_PURPOSE_FIELD "y")
set(CONFIG_SOC_SDMMC_HOST_SUPPORTED "y")
set(CONFIG_SOC_RTC_FAST_MEM_SUPPORTED "y")
set(CONFIG_SOC_RTC_SLOW_MEM_SUPPORTED "y")
set(CONFIG_SOC_RTC_MEM_SUPPORTED "y")
set(CONFIG_SOC_PSRAM_DMA_CAPABLE "y")
set(CONFIG_SOC_XT_WDT_SUPPORTED "y")
set(CONFIG_SOC_I2S_SUPPORTED "y")
set(CONFIG_SOC_RMT_SUPPORTED "y")
set(CONFIG_SOC_SDM_SUPPORTED "y")
set(CONFIG_SOC_GPSPI_SUPPORTED "y")
set(CONFIG_SOC_LEDC_SUPPORTED "y")
set(CONFIG_SOC_I2C_SUPPORTED "y")
set(CONFIG_SOC_SYSTIMER_SUPPORTED "y")
set(CONFIG_SOC_SUPPORT_COEXISTENCE "y")
set(CONFIG_SOC_TEMP_SENSOR_SUPPORTED "y")
set(CONFIG_SOC_AES_SUPPORTED "y")
set(CONFIG_SOC_MPI_SUPPORTED "y")
set(CONFIG_SOC_SHA_SUPPORTED "y")
set(CONFIG_SOC_HMAC_SUPPORTED "y")
set(CONFIG_SOC_DIG_SIGN_SUPPORTED "y")
set(CONFIG_SOC_FLASH_ENC_SUPPORTED "y")
set(CONFIG_SOC_SECURE_BOOT_SUPPORTED "y")
set(CONFIG_SOC_MEMPROT_SUPPORTED "y")
set(CONFIG_SOC_TOUCH_SENSOR_SUPPORTED "y")
set(CONFIG_SOC_BOD_SUPPORTED "y")
set(CONFIG_SOC_XTAL_SUPPORT_40M "y")
set(CONFIG_SOC_APPCPU_HAS_CLOCK_GATING_BUG "y")
set(CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED "y")
set(CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED "y")
set(CONFIG_SOC_ADC_ARBITER_SUPPORTED "y")
set(CONFIG_SOC_ADC_DIG_IIR_FILTER_SUPPORTED "y")
set(CONFIG_SOC_ADC_MONITOR_SUPPORTED "y")
set(CONFIG_SOC_ADC_DMA_SUPPORTED "y")
set(CONFIG_SOC_ADC_PERIPH_NUM "2")
set(CONFIG_SOC_ADC_MAX_CHANNEL_NUM "10")
set(CONFIG_SOC_ADC_ATTEN_NUM "4")
set(CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM "2")
set(CONFIG_SOC_ADC_PATT_LEN_MAX "24")
set(CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH "12")
set(CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH "12")
set(CONFIG_SOC_ADC_DIGI_RESULT_BYTES "4")
set(CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV "4")
set(CONFIG_SOC_ADC_DIGI_IIR_FILTER_NUM "2")
set(CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH "83333")
set(CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW "611")
set(CONFIG_SOC_ADC_RTC_MIN_BITWIDTH "12")
set(CONFIG_SOC_ADC_RTC_MAX_BITWIDTH "12")
set(CONFIG_SOC_ADC_CALIBRATION_V1_SUPPORTED "y")
set(CONFIG_SOC_ADC_SELF_HW_CALI_SUPPORTED "y")
set(CONFIG_SOC_APB_BACKUP_DMA "y")
set(CONFIG_SOC_BROWNOUT_RESET_SUPPORTED "y")
set(CONFIG_SOC_CACHE_WRITEBACK_SUPPORTED "y")
set(CONFIG_SOC_CACHE_FREEZE_SUPPORTED "y")
set(CONFIG_SOC_CPU_CORES_NUM "2")
set(CONFIG_SOC_CPU_INTR_NUM "32")
set(CONFIG_SOC_CPU_HAS_FPU "y")
set(CONFIG_SOC_CPU_BREAKPOINTS_NUM "2")
set(CONFIG_SOC_CPU_WATCHPOINTS_NUM "2")
set(CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE "64")
set(CONFIG_SOC_DS_SIGNATURE_MAX_BIT_LEN "4096")
set(CONFIG_SOC_DS_KEY_PARAM_MD_IV_LENGTH "16")
set(CONFIG_SOC_DS_KEY_CHECK_MAX_WAIT_US "1100")
set(CONFIG_SOC_GDMA_GROUPS "y")
set(CONFIG_SOC_GDMA_PAIRS_PER_GROUP "5")
set(CONFIG_SOC_GDMA_SUPPORT_PSRAM "y")
set(CONFIG_SOC_GPIO_PORT "1")
set(CONFIG_SOC_GPIO_PIN_COUNT "49")
set(CONFIG_SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER "y")
set(CONFIG_SOC_GPIO_FILTER_CLK_SUPPORT_APB "y")
set(CONFIG_SOC_GPIO_SUPPORT_RTC_INDEPENDENT "y")
set(CONFIG_SOC_GPIO_SUPPORT_FORCE_HOLD "y")
set(CONFIG_SOC_GPIO_VALID_GPIO_MASK "0x1ffffffffffff")
set(CONFIG_SOC_GPIO_IN_RANGE_MAX "48")
set(CONFIG_SOC_GPIO_OUT_RANGE_MAX "48")
set(CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK "0x1fffffc000000")
set(CONFIG_SOC_DEDIC_GPIO_OUT_CHANNELS_NUM "8")
set(CONFIG_SOC_DEDIC_GPIO_IN_CHANNELS_NUM "8")
set(CONFIG_SOC_DEDIC_GPIO_OUT_AUTO_ENABLE "y")
set(CONFIG_SOC_I2C_NUM "2")
set(CONFIG_SOC_I2C_FIFO_LEN "32")
set(CONFIG_SOC_I2C_CMD_REG_NUM "8")
set(CONFIG_SOC_I2C_SUPPORT_SLAVE "y")
set(CONFIG_SOC_I2C_SUPPORT_HW_CLR_BUS "y")
set(CONFIG_SOC_I2C_SUPPORT_XTAL "y")
set(CONFIG_SOC_I2C_SUPPORT_RTC "y")
set(CONFIG_SOC_I2S_NUM "2")
set(CONFIG_SOC_I2S_HW_VERSION_2 "y")
set(CONFIG_SOC_I2S_SUPPORTS_XTAL "y")
set(CONFIG_SOC_I2S_SUPPORTS_PLL_F160M "y")
set(CONFIG_SOC_I2S_SUPPORTS_PCM "y")
set(CONFIG_SOC_I2S_SUPPORTS_PDM "y")
set(CONFIG_SOC_I2S_SUPPORTS_PDM_TX "y")
set(CONFIG_SOC_I2S_PDM_MAX_TX_LINES "2")
set(CONFIG_SOC_I2S_SUPPORTS_PDM_RX "y")
set(CONFIG_SOC_I2S_PDM_MAX_RX_LINES "4")
set(CONFIG_SOC_I2S_SUPPORTS_TDM "y")
set(CONFIG_SOC_LEDC_SUPPORT_APB_CLOCK "y")
set(CONFIG_SOC_LEDC_SUPPORT_XTAL_CLOCK "y")
set(CONFIG_SOC_LEDC_CHANNEL_NUM "8")
set(CONFIG_SOC_LEDC_TIMER_BIT_WIDTH "14")
set(CONFIG_SOC_LEDC_SUPPORT_FADE_STOP "y")
set(CONFIG_SOC_MCPWM_GROUPS "2")
set(CONFIG_SOC_MCPWM_TIMERS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP "y")
set(CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER "3")
set(CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_SWSYNC_CAN_PROPAGATE "y")
set(CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM "1")
set(CONFIG_SOC_MMU_PERIPH_NUM "1")
set(CONFIG_SOC_PCNT_GROUPS "1")
set(CONFIG_SOC_PCNT_UNITS_PER_GROUP "4")
set(CONFIG_SOC_PCNT_CHANNELS_PER_UNIT "2")
set(CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT "2")
set(CONFIG_SOC_RMT_GROUPS "1")
set(CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP "4")
set(CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP "4")
set(CONFIG_SOC_RMT_CHANNELS_PER_GROUP "8")
set(CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL "48")
set(CONFIG_SOC_RMT_SUPPORT_RX_PINGPONG "y")
set(CONFIG_SOC_RMT_SUPPORT_RX_DEMODULATION "y")
set(CONFIG_SOC_RMT_SUPPORT_TX_ASYNC_STOP "y")
set(CONFIG_SOC_RMT_SUPPORT_TX_LOOP_COUNT "y")
set(CONFIG_SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP "y")
set(CONFIG_SOC_RMT_SUPPORT_TX_SYNCHRO "y")
set(CONFIG_SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY "y")
set(CONFIG_SOC_RMT_SUPPORT_XTAL "y")
set(CONFIG_SOC_RMT_SUPPORT_RC_FAST "y")
set(CONFIG_SOC_RMT_SUPPORT_APB "y")
set(CONFIG_SOC_RMT_SUPPORT_DMA "y")
set(CONFIG_SOC_LCD_I80_SUPPORTED "y")
set(CONFIG_SOC_LCD_RGB_SUPPORTED "y")
set(CONFIG_SOC_LCD_I80_BUSES "1")
set(CONFIG_SOC_LCD_RGB_PANELS "1")
set(CONFIG_SOC_LCD_I80_BUS_WIDTH "16")
set(CONFIG_SOC_LCD_RGB_DATA_WIDTH "16")
set(CONFIG_SOC_LCD_SUPPORT_RGB_YUV_CONV "y")
set(CONFIG_SOC_RTC_CNTL_CPU_PD_DMA_BUS_WIDTH "128")
set(CONFIG_SOC_RTC_CNTL_CPU_PD_REG_FILE_NUM "549")
set(CONFIG_SOC_RTC_CNTL_TAGMEM_PD_DMA_BUS_WIDTH "128")
set(CONFIG_SOC_RTCIO_PIN_COUNT "22")
set(CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED "y")
set(CONFIG_SOC_RTCIO_HOLD_SUPPORTED "y")
set(CONFIG_SOC_RTCIO_WAKE_SUPPORTED "y")
set(CONFIG_SOC_SDM_GROUPS "y")
set(CONFIG_SOC_SDM_CHANNELS_PER_GROUP "8")
set(CONFIG_SOC_SDM_CLK_SUPPORT_APB "y")
set(CONFIG_SOC_SPI_PERIPH_NUM "3")
set(CONFIG_SOC_SPI_MAX_CS_NUM "6")
set(CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE "64")
set(CONFIG_SOC_SPI_SUPPORT_DDRCLK "y")
set(CONFIG_SOC_SPI_SLAVE_SUPPORT_SEG_TRANS "y")
set(CONFIG_SOC_SPI_SUPPORT_CD_SIG "y")
set(CONFIG_SOC_SPI_SUPPORT_CONTINUOUS_TRANS "y")
set(CONFIG_SOC_SPI_SUPPORT_SLAVE_HD_VER2 "y")
set(CONFIG_SOC_SPI_SUPPORT_CLK_APB "y")
set(CONFIG_SOC_SPI_SUPPORT_CLK_XTAL "y")
set(CONFIG_SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT "y")
set(CONFIG_SOC_MEMSPI_IS_INDEPENDENT "y")
set(CONFIG_SOC_SPI_MAX_PRE_DIVIDER "16")
set(CONFIG_SOC_SPI_SUPPORT_OCT "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_120M "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED "y")
set(CONFIG_SOC_SPIRAM_SUPPORTED "y")
set(CONFIG_SOC_SPIRAM_XIP_SUPPORTED "y")
set(CONFIG_SOC_SYSTIMER_COUNTER_NUM "2")
set(CONFIG_SOC_SYSTIMER_ALARM_NUM "3")
set(CONFIG_SOC_SYSTIMER_BIT_WIDTH_LO "32")
set(CONFIG_SOC_SYSTIMER_BIT_WIDTH_HI "20")
set(CONFIG_SOC_SYSTIMER_FIXED_DIVIDER "y")
set(CONFIG_SOC_SYSTIMER_INT_LEVEL "y")
set(CONFIG_SOC_SYSTIMER_ALARM_MISS_COMPENSATE "y")
set(CONFIG_SOC_TIMER_GROUPS "2")
set(CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP "2")
set(CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH "54")
set(CONFIG_SOC_TIMER_GROUP_SUPPORT_XTAL "y")
set(CONFIG_SOC_TIMER_GROUP_SUPPORT_APB "y")
set(CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS "4")
set(CONFIG_SOC_TOUCH_VERSION_2 "y")
set(CONFIG_SOC_TOUCH_SENSOR_NUM "15")
set(CONFIG_SOC_TOUCH_PROXIMITY_CHANNEL_NUM "3")
set(CONFIG_SOC_TOUCH_PROXIMITY_MEAS_DONE_SUPPORTED "y")
set(CONFIG_SOC_TOUCH_PAD_THRESHOLD_MAX "0x1fffff")
set(CONFIG_SOC_TOUCH_PAD_MEASURE_WAIT_MAX "0xff")
set(CONFIG_SOC_TWAI_CONTROLLER_NUM "1")
set(CONFIG_SOC_TWAI_CLK_SUPPORT_APB "y")
set(CONFIG_SOC_TWAI_BRP_MIN "2")
set(CONFIG_SOC_TWAI_BRP_MAX "16384")
set(CONFIG_SOC_TWAI_SUPPORTS_RX_STATUS "y")
set(CONFIG_SOC_UART_NUM "3")
set(CONFIG_SOC_UART_FIFO_LEN "128")
set(CONFIG_SOC_UART_BITRATE_MAX "5000000")
set(CONFIG_SOC_UART_SUPPORT_FSM_TX_WAIT_SEND "y")
set(CONFIG_SOC_UART_SUPPORT_WAKEUP_INT "y")
set(CONFIG_SOC_UART_SUPPORT_APB_CLK "y")
set(CONFIG_SOC_UART_SUPPORT_RTC_CLK "y")
set(CONFIG_SOC_UART_SUPPORT_XTAL_CLK "y")
set(CONFIG_SOC_UART_REQUIRE_CORE_RESET "y")
set(CONFIG_SOC_USB_OTG_PERIPH_NUM "1")
set(CONFIG_SOC_SHA_DMA_MAX_BUFFER_SIZE "3968")
set(CONFIG_SOC_SHA_SUPPORT_DMA "y")
set(CONFIG_SOC_SHA_SUPPORT_RESUME "y")
set(CONFIG_SOC_SHA_GDMA "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA1 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA224 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA256 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA384 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA512 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA512_224 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA512_256 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA512_T "y")
set(CONFIG_SOC_RSA_MAX_BIT_LEN "4096")
set(CONFIG_SOC_AES_SUPPORT_DMA "y")
set(CONFIG_SOC_AES_GDMA "y")
set(CONFIG_SOC_AES_SUPPORT_AES_128 "y")
set(CONFIG_SOC_AES_SUPPORT_AES_256 "y")
set(CONFIG_SOC_PM_SUPPORT_EXT0_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_EXT_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_WIFI_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_BT_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_CPU_PD "y")
set(CONFIG_SOC_PM_SUPPORT_TAGMEM_PD "y")
set(CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD "y")
set(CONFIG_SOC_PM_SUPPORT_RC_FAST_PD "y")
set(CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD "y")
set(CONFIG_SOC_PM_SUPPORT_MAC_BB_PD "y")
set(CONFIG_SOC_PM_SUPPORT_MODEM_PD "y")
set(CONFIG_SOC_CONFIGURABLE_VDDSDIO_SUPPORTED "y")
set(CONFIG_SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY "y")
set(CONFIG_SOC_PM_CPU_RETENTION_BY_RTCCNTL "y")
set(CONFIG_SOC_PM_MODEM_RETENTION_BY_BACKUPDMA "y")
set(CONFIG_SOC_CLK_RC_FAST_D256_SUPPORTED "y")
set(CONFIG_SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256 "y")
set(CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION "y")
set(CONFIG_SOC_CLK_XTAL32K_SUPPORTED "y")
set(CONFIG_SOC_EFUSE_DIS_DOWNLOAD_ICACHE "y")
set(CONFIG_SOC_EFUSE_DIS_DOWNLOAD_DCACHE "y")
set(CONFIG_SOC_EFUSE_HARD_DIS_JTAG "y")
set(CONFIG_SOC_EFUSE_DIS_USB_JTAG "y")
set(CONFIG_SOC_EFUSE_SOFT_DIS_JTAG "y")
set(CONFIG_SOC_EFUSE_DIS_DIRECT_BOOT "y")
set(CONFIG_SOC_EFUSE_DIS_ICACHE "y")
set(CONFIG_SOC_EFUSE_BLOCK9_KEY_PURPOSE_QUIRK "y")
set(CONFIG_SOC_SECURE_BOOT_V2_RSA "y")
set(CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS "3")
set(CONFIG_SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS "y")
set(CONFIG_SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY "y")
set(CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX "64")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES "y")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS "y")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_128 "y")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_256 "y")
set(CONFIG_SOC_MEMPROT_CPU_PREFETCH_PAD_SIZE "16")
set(CONFIG_SOC_MEMPROT_MEM_ALIGN_SIZE "256")
set(CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE "21")
set(CONFIG_SOC_MAC_BB_PD_MEM_SIZE "192")
set(CONFIG_SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH "12")
set(CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_RESUME "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_SW_SUSPEND "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_OPI_MODE "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_TIME_TUNING "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_WRAP "y")
set(CONFIG_SOC_COEX_HW_PTI "y")
set(CONFIG_SOC_EXTERNAL_COEX_LEADER_TX_LINE "y")
set(CONFIG_SOC_SDMMC_USE_GPIO_MATRIX "y")
set(CONFIG_SOC_SDMMC_NUM_SLOTS "2")
set(CONFIG_SOC_SDMMC_SUPPORT_XTAL_CLOCK "y")
set(CONFIG_SOC_TEMPERATURE_SENSOR_SUPPORT_FAST_RC "y")
set(CONFIG_SOC_WIFI_HW_TSF "y")
set(CONFIG_SOC_WIFI_FTM_SUPPORT "y")
set(CONFIG_SOC_WIFI_GCMP_SUPPORT "y")
set(CONFIG_SOC_WIFI_WAPI_SUPPORT "y")
set(CONFIG_SOC_WIFI_CSI_SUPPORT "y")
set(CONFIG_SOC_WIFI_MESH_SUPPORT "y")
set(CONFIG_SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW "y")
set(CONFIG_SOC_WIFI_PHY_NEEDS_USB_WORKAROUND "y")
set(CONFIG_SOC_BLE_SUPPORTED "y")
set(CONFIG_SOC_BLE_MESH_SUPPORTED "y")
set(CONFIG_SOC_BLE_50_SUPPORTED "y")
set(CONFIG_SOC_BLE_DEVICE_PRIVACY_SUPPORTED "y")
set(CONFIG_SOC_BLUFI_SUPPORTED "y")
set(CONFIG_SOC_ULP_HAS_ADC "y")
set(CONFIG_SOC_PHY_COMBO_MODULE "y")
set(CONFIG_IDF_CMAKE "y")
set(CONFIG_IDF_TARGET_ARCH_XTENSA "y")
set(CONFIG_IDF_TARGET_ARCH "xtensa")
set(CONFIG_IDF_TARGET "esp32s3")
set(CONFIG_IDF_TARGET_ESP32S3 "y")
set(CONFIG_IDF_FIRMWARE_CHIP_ID "0x9")
set(CONFIG_APP_BUILD_TYPE_APP_2NDBOOT "y")
set(CONFIG_APP_BUILD_TYPE_RAM "")
set(CONFIG_APP_BUILD_GENERATE_BINARIES "y")
set(CONFIG_APP_BUILD_BOOTLOADER "y")
set(CONFIG_APP_BUILD_USE_FLASH_SECTIONS "y")
set(CONFIG_APP_REPRODUCIBLE_BUILD "")
set(CONFIG_APP_NO_BLOBS "")
set(CONFIG_BOOTLOADER_OFFSET_IN_FLASH "0x0")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE "y")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG "")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF "")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_NONE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_NONE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_ERROR "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_WARN "y")
set(CONFIG_BOOTLOADER_LOG_LEVEL_INFO "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL "2")
set(CONFIG_BOOTLOADER_FLASH_DC_AWARE "")
set(CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT "y")
set(CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_9V "y")
set(CONFIG_BOOTLOADER_FACTORY_RESET "")
set(CONFIG_BOOTLOADER_APP_TEST "")
set(CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE "y")
set(CONFIG_BOOTLOADER_WDT_ENABLE "y")
set(CONFIG_BOOTLOADER_WDT_DISABLE_IN_USER_CODE "")
set(CONFIG_BOOTLOADER_WDT_TIME_MS "9000")
set(CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS "")
set(CONFIG_BOOTLOADER_RESERVE_RTC_SIZE "0x0")
set(CONFIG_BOOTLOADER_CUSTOM_RESERVE_RTC "")
set(CONFIG_SECURE_BOOT_V2_RSA_SUPPORTED "y")
set(CONFIG_SECURE_BOOT_V2_PREFERRED "y")
set(CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT "")
set(CONFIG_SECURE_BOOT "")
set(CONFIG_SECURE_FLASH_ENC_ENABLED "")
set(CONFIG_SECURE_ROM_DL_MODE_ENABLED "y")
set(CONFIG_APP_COMPILE_TIME_DATE "y")
set(CONFIG_APP_EXCLUDE_PROJECT_VER_VAR "")
set(CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR "")
set(CONFIG_APP_PROJECT_VER_FROM_CONFIG "")
set(CONFIG_APP_RETRIEVE_LEN_ELF_SHA "16")
set(CONFIG_ESP_ROM_HAS_CRC_LE "y")
set(CONFIG_ESP_ROM_HAS_CRC_BE "y")
set(CONFIG_ESP_ROM_HAS_MZ_CRC32 "y")
set(CONFIG_ESP_ROM_HAS_JPEG_DECODE "y")
set(CONFIG_ESP_ROM_UART_CLK_IS_XTAL "y")
set(CONFIG_ESP_ROM_HAS_RETARGETABLE_LOCKING "y")
set(CONFIG_ESP_ROM_USB_OTG_NUM "3")
set(CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM "4")
set(CONFIG_ESP_ROM_HAS_ERASE_0_REGION_BUG "y")
set(CONFIG_ESP_ROM_HAS_ENCRYPTED_WRITES_USING_LEGACY_DRV "y")
set(CONFIG_ESP_ROM_GET_CLK_FREQ "y")
set(CONFIG_ESP_ROM_HAS_HAL_WDT "y")
set(CONFIG_ESP_ROM_NEEDS_SWSETUP_WORKAROUND "y")
set(CONFIG_ESP_ROM_HAS_LAYOUT_TABLE "y")
set(CONFIG_ESP_ROM_HAS_SPI_FLASH "y")
set(CONFIG_ESP_ROM_HAS_ETS_PRINTF_BUG "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT "y")
set(CONFIG_ESP_ROM_NEEDS_SET_CACHE_MMU_SIZE "y")
set(CONFIG_ESP_ROM_RAM_APP_NEEDS_MMU_INIT "y")
set(CONFIG_ESP_ROM_HAS_FLASH_COUNT_PAGES_BUG "y")
set(CONFIG_ESP_ROM_HAS_CACHE_SUSPEND_WAITI_BUG "y")
set(CONFIG_ESP_ROM_HAS_CACHE_WRITEBACK_BUG "y")
set(CONFIG_BOOT_ROM_LOG_ALWAYS_ON "y")
set(CONFIG_BOOT_ROM_LOG_ALWAYS_OFF "")
set(CONFIG_BOOT_ROM_LOG_ON_GPIO_HIGH "")
set(CONFIG_BOOT_ROM_LOG_ON_GPIO_LOW "")
set(CONFIG_ESPTOOLPY_NO_STUB "")
set(CONFIG_ESPTOOLPY_OCT_FLASH "")
set(CONFIG_ESPTOOLPY_FLASH_MODE_AUTO_DETECT "y")
set(CONFIG_ESPTOOLPY_FLASHMODE_QIO "")
set(CONFIG_ESPTOOLPY_FLASHMODE_QOUT "")
set(CONFIG_ESPTOOLPY_FLASHMODE_DIO "y")
set(CONFIG_ESPTOOLPY_FLASHMODE_DOUT "")
set(CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR "y")
set(CONFIG_ESPTOOLPY_FLASHMODE "dio")
set(CONFIG_ESPTOOLPY_FLASHFREQ_120M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ_80M "y")
set(CONFIG_ESPTOOLPY_FLASHFREQ_40M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ_20M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ_80M_DEFAULT "y")
set(CONFIG_ESPTOOLPY_FLASHFREQ "80m")
set(CONFIG_ESPTOOLPY_FLASHSIZE_1MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_2MB "y")
set(CONFIG_ESPTOOLPY_FLASHSIZE_4MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_8MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_16MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_32MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_64MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_128MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE "2MB")
set(CONFIG_ESPTOOLPY_HEADER_FLASHSIZE_UPDATE "")
set(CONFIG_ESPTOOLPY_BEFORE_RESET "y")
set(CONFIG_ESPTOOLPY_BEFORE_NORESET "")
set(CONFIG_ESPTOOLPY_BEFORE "default_reset")
set(CONFIG_ESPTOOLPY_AFTER_RESET "y")
set(CONFIG_ESPTOOLPY_AFTER_NORESET "")
set(CONFIG_ESPTOOLPY_AFTER "hard_reset")
set(CONFIG_ESPTOOLPY_MONITOR_BAUD "115200")
set(CONFIG_PARTITION_TABLE_SINGLE_APP "")
set(CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE "")
set(CONFIG_PARTITION_TABLE_TWO_OTA "")
set(CONFIG_PARTITION_TABLE_CUSTOM "y")
set(CONFIG_PARTITION_TABLE_CUSTOM_FILENAME "partitions_example.csv")
set(CONFIG_PARTITION_TABLE_FILENAME "partitions_example.csv")
set(CONFIG_PARTITION_TABLE_OFFSET "0x8000")
set(CONFIG_PARTITION_TABLE_MD5 "y")
set(CONFIG_COMPILER_OPTIMIZATION_DEFAULT "y")
set(CONFIG_COMPILER_OPTIMIZATION_SIZE "")
set(CONFIG_COMPILER_OPTIMIZATION_PERF "")
set(CONFIG_COMPILER_OPTIMIZATION_NONE "")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE "y")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT "")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE "")
set(CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB "y")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL "2")
set(CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT "")
set(CONFIG_COMPILER_HIDE_PATHS_MACROS "y")
set(CONFIG_COMPILER_CXX_EXCEPTIONS "")
set(CONFIG_COMPILER_CXX_RTTI "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_NONE "y")
set(CONFIG_COMPILER_STACK_CHECK_MODE_NORM "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_STRONG "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_ALL "")
set(CONFIG_COMPILER_WARN_WRITE_STRINGS "")
set(CONFIG_COMPILER_DISABLE_GCC12_WARNINGS "")
set(CONFIG_COMPILER_DUMP_RTL_FILES "")
set(CONFIG_EFUSE_CUSTOM_TABLE "")
set(CONFIG_EFUSE_VIRTUAL "")
set(CONFIG_EFUSE_MAX_BLK_LEN "256")
set(CONFIG_ESP_ERR_TO_NAME_LOOKUP "y")
set(CONFIG_ESP32S3_REV_MIN_0 "y")
set(CONFIG_ESP32S3_REV_MIN_1 "")
set(CONFIG_ESP32S3_REV_MIN_2 "")
set(CONFIG_ESP32S3_REV_MIN_FULL "0")
set(CONFIG_ESP_REV_MIN_FULL "0")
set(CONFIG_ESP32S3_REV_MAX_FULL "99")
set(CONFIG_ESP_REV_MAX_FULL "99")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_STA "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_AP "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_BT "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH "y")
set(CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR "y")
set(CONFIG_ESP32S3_UNIVERSAL_MAC_ADDRESSES_TWO "")
set(CONFIG_ESP32S3_UNIVERSAL_MAC_ADDRESSES_FOUR "y")
set(CONFIG_ESP32S3_UNIVERSAL_MAC_ADDRESSES "4")
set(CONFIG_ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC "")
set(CONFIG_ESP_SLEEP_POWER_DOWN_FLASH "")
set(CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND "y")
set(CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU "y")
set(CONFIG_ESP_SLEEP_RTC_BUS_ISO_WORKAROUND "y")
set(CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND "y")
set(CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY "2000")
set(CONFIG_ESP_SLEEP_DEBUG "")
set(CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS "y")
set(CONFIG_ESP_SLEEP_CACHE_SAFE_ASSERTION "")
set(CONFIG_RTC_CLK_SRC_INT_RC "y")
set(CONFIG_RTC_CLK_SRC_EXT_CRYS "")
set(CONFIG_RTC_CLK_SRC_EXT_OSC "")
set(CONFIG_RTC_CLK_SRC_INT_8MD256 "")
set(CONFIG_RTC_CLK_CAL_CYCLES "1024")
set(CONFIG_PERIPH_CTRL_FUNC_IN_IRAM "y")
set(CONFIG_GDMA_CTRL_FUNC_IN_IRAM "")
set(CONFIG_GDMA_ISR_IRAM_SAFE "")
set(CONFIG_XTAL_FREQ_40 "y")
set(CONFIG_XTAL_FREQ "40")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80 "")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160 "y")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240 "")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ "160")
set(CONFIG_ESP32S3_INSTRUCTION_CACHE_16KB "y")
set(CONFIG_ESP32S3_INSTRUCTION_CACHE_32KB "")
set(CONFIG_ESP32S3_INSTRUCTION_CACHE_SIZE "0x4000")
set(CONFIG_ESP32S3_INSTRUCTION_CACHE_4WAYS "")
set(CONFIG_ESP32S3_INSTRUCTION_CACHE_8WAYS "y")
set(CONFIG_ESP32S3_ICACHE_ASSOCIATED_WAYS "8")
set(CONFIG_ESP32S3_INSTRUCTION_CACHE_LINE_16B "")
set(CONFIG_ESP32S3_INSTRUCTION_CACHE_LINE_32B "y")
set(CONFIG_ESP32S3_INSTRUCTION_CACHE_LINE_SIZE "32")
set(CONFIG_ESP32S3_DATA_CACHE_16KB "")
set(CONFIG_ESP32S3_DATA_CACHE_32KB "y")
set(CONFIG_ESP32S3_DATA_CACHE_64KB "")
set(CONFIG_ESP32S3_DATA_CACHE_SIZE "0x8000")
set(CONFIG_ESP32S3_DATA_CACHE_4WAYS "")
set(CONFIG_ESP32S3_DATA_CACHE_8WAYS "y")
set(CONFIG_ESP32S3_DCACHE_ASSOCIATED_WAYS "8")
set(CONFIG_ESP32S3_DATA_CACHE_LINE_16B "")
set(CONFIG_ESP32S3_DATA_CACHE_LINE_32B "y")
set(CONFIG_ESP32S3_DATA_CACHE_LINE_64B "")
set(CONFIG_ESP32S3_DATA_CACHE_LINE_SIZE "32")
set(CONFIG_ESP32S3_RTCDATA_IN_FAST_MEM "")
set(CONFIG_ESP32S3_USE_FIXED_STATIC_RAM_SIZE "")
set(CONFIG_ESP32S3_TRAX "")
set(CONFIG_ESP32S3_TRACEMEM_RESERVE_DRAM "0x0")
set(CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT "")
set(CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT "y")
set(CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT "")
set(CONFIG_ESP_SYSTEM_PANIC_GDBSTUB "")
set(CONFIG_ESP_SYSTEM_GDBSTUB_RUNTIME "")
set(CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS "0")
set(CONFIG_ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK "y")
set(CONFIG_ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP "y")
set(CONFIG_ESP_SYSTEM_MEMPROT_FEATURE "y")
set(CONFIG_ESP_SYSTEM_MEMPROT_FEATURE_LOCK "y")
set(CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE "32")
set(CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE "2304")
set(CONFIG_ESP_MAIN_TASK_STACK_SIZE "7168")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0 "y")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_CPU1 "")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_NO_AFFINITY "")
set(CONFIG_ESP_MAIN_TASK_AFFINITY "0x0")
set(CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE "2048")
set(CONFIG_ESP_CONSOLE_UART_DEFAULT "y")
set(CONFIG_ESP_CONSOLE_USB_CDC "")
set(CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG "")
set(CONFIG_ESP_CONSOLE_UART_CUSTOM "")
set(CONFIG_ESP_CONSOLE_NONE "")
set(CONFIG_ESP_CONSOLE_SECONDARY_NONE "")
set(CONFIG_ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG "y")
set(CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED "y")
set(CONFIG_ESP_CONSOLE_UART "y")
set(CONFIG_ESP_CONSOLE_MULTIPLE_UART "y")
set(CONFIG_ESP_CONSOLE_UART_NUM "0")
set(CONFIG_ESP_CONSOLE_UART_BAUDRATE "115200")
set(CONFIG_ESP_INT_WDT "y")
set(CONFIG_ESP_INT_WDT_TIMEOUT_MS "300")
set(CONFIG_ESP_INT_WDT_CHECK_CPU1 "y")
set(CONFIG_ESP_TASK_WDT_EN "y")
set(CONFIG_ESP_TASK_WDT_INIT "y")
set(CONFIG_ESP_TASK_WDT_PANIC "")
set(CONFIG_ESP_TASK_WDT_TIMEOUT_S "5")
set(CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0 "y")
set(CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1 "y")
set(CONFIG_ESP_PANIC_HANDLER_IRAM "")
set(CONFIG_ESP_DEBUG_STUBS_ENABLE "")
set(CONFIG_ESP_DEBUG_OCDAWARE "y")
set(CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4 "y")
set(CONFIG_ESP_BROWNOUT_DET "y")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7 "y")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_4 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_3 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_2 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_1 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL "7")
set(CONFIG_ESP_SYSTEM_BROWNOUT_INTR "y")
set(CONFIG_ESP_SYSTEM_BBPLL_RECALIB "y")
set(CONFIG_ESP_IPC_TASK_STACK_SIZE "1280")
set(CONFIG_ESP_IPC_USES_CALLERS_PRIORITY "y")
set(CONFIG_ESP_IPC_ISR_ENABLE "y")
set(CONFIG_FREERTOS_SMP "")
set(CONFIG_FREERTOS_UNICORE "")
set(CONFIG_FREERTOS_HZ "100")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE "")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL "")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY "y")
set(CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS "1")
set(CONFIG_FREERTOS_IDLE_TASK_STACKSIZE "1536")
set(CONFIG_FREERTOS_USE_IDLE_HOOK "")
set(CONFIG_FREERTOS_USE_TICK_HOOK "")
set(CONFIG_FREERTOS_MAX_TASK_NAME_LEN "16")
set(CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY "")
set(CONFIG_FREERTOS_TIMER_TASK_PRIORITY "1")
set(CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH "2048")
set(CONFIG_FREERTOS_TIMER_QUEUE_LENGTH "10")
set(CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE "0")
set(CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES "1")
set(CONFIG_FREERTOS_USE_TRACE_FACILITY "y")
set(CONFIG_FREERTOS_USE_STATS_FORMATTING_FUNCTIONS "y")
set(CONFIG_FREERTOS_VTASKLIST_INCLUDE_COREID "")
set(CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS "")
set(CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER "y")
set(CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK "")
set(CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS "y")
set(CONFIG_FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP "")
set(CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER "y")
set(CONFIG_FREERTOS_ISR_STACKSIZE "1536")
set(CONFIG_FREERTOS_INTERRUPT_BACKTRACE "y")
set(CONFIG_FREERTOS_TICK_SUPPORT_SYSTIMER "y")
set(CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL1 "y")
set(CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL3 "")
set(CONFIG_FREERTOS_SYSTICK_USES_SYSTIMER "y")
set(CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH "")
set(CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH "")
set(CONFIG_FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE "")
set(CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT "y")
set(CONFIG_FREERTOS_NO_AFFINITY "0x7fffffff")
set(CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION "y")
set(CONFIG_FREERTOS_DEBUG_OCDAWARE "y")
set(CONFIG_HAL_ASSERTION_EQUALS_SYSTEM "y")
set(CONFIG_HAL_ASSERTION_DISABLE "")
set(CONFIG_HAL_ASSERTION_SILENT "")
set(CONFIG_HAL_ASSERTION_ENABLE "")
set(CONFIG_HAL_DEFAULT_ASSERTION_LEVEL "2")
set(CONFIG_HAL_WDT_USE_ROM_IMPL "y")
set(CONFIG_LOG_DEFAULT_LEVEL_NONE "")
set(CONFIG_LOG_DEFAULT_LEVEL_ERROR "")
set(CONFIG_LOG_DEFAULT_LEVEL_WARN "")
set(CONFIG_LOG_DEFAULT_LEVEL_INFO "y")
set(CONFIG_LOG_DEFAULT_LEVEL_DEBUG "")
set(CONFIG_LOG_DEFAULT_LEVEL_VERBOSE "")
set(CONFIG_LOG_DEFAULT_LEVEL "3")
set(CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT "y")
set(CONFIG_LOG_MAXIMUM_LEVEL_DEBUG "")
set(CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE "")
set(CONFIG_LOG_MAXIMUM_LEVEL "3")
set(CONFIG_LOG_COLORS "y")
set(CONFIG_LOG_TIMESTAMP_SOURCE_RTOS "y")
set(CONFIG_LOG_TIMESTAMP_SOURCE_SYSTEM "")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF "y")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF "")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_CR "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_CRLF "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_LF "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_CR "y")
set(CONFIG_NEWLIB_NANO_FORMAT "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT "y")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE "")
set(CONFIG_MMU_PAGE_SIZE_64KB "y")
set(CONFIG_MMU_PAGE_MODE "64KB")
set(CONFIG_MMU_PAGE_SIZE "0x10000")
set(CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC "y")
set(CONFIG_SPI_FLASH_BROWNOUT_RESET "y")
set(CONFIG_SPI_FLASH_HPM_ENA "")
set(CONFIG_SPI_FLASH_HPM_AUTO "y")
set(CONFIG_SPI_FLASH_HPM_DIS "")
set(CONFIG_SPI_FLASH_HPM_ON "y")
set(CONFIG_SPI_FLASH_HPM_DC_AUTO "y")
set(CONFIG_SPI_FLASH_HPM_DC_DISABLE "")
set(CONFIG_SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND "")
set(CONFIG_SPI_FLASH_VERIFY_WRITE "")
set(CONFIG_SPI_FLASH_ENABLE_COUNTERS "")
set(CONFIG_SPI_FLASH_ROM_DRIVER_PATCH "y")
set(CONFIG_SPI_FLASH_ROM_IMPL "")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS "y")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS "")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED "")
set(CONFIG_SPI_FLASH_BYPASS_BLOCK_ERASE "")
set(CONFIG_SPI_FLASH_YIELD_DURING_ERASE "y")
set(CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS "20")
set(CONFIG_SPI_FLASH_ERASE_YIELD_TICKS "1")
set(CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE "8192")
set(CONFIG_SPI_FLASH_SIZE_OVERRIDE "")
set(CONFIG_SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED "")
set(CONFIG_SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST "")
set(CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_GD_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_ISSI_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_MXIC_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_WINBOND_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_BOYA_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_TH_SUPPORTED "y")
set(CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_GD_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_TH_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_MXIC_OPI_CHIP "y")
set(CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE "y")
set(CONFIG_IDF_EXPERIMENTAL_FEATURES "")
set(CONFIGS_LIST CONFIG_SOC_MPU_MIN_REGION_SIZE;CONFIG_SOC_MPU_REGIONS_MAX_NUM;CONFIG_SOC_ADC_SUPPORTED;CONFIG_SOC_UART_SUPPORTED;CONFIG_SOC_PCNT_SUPPORTED;CONFIG_SOC_WIFI_SUPPORTED;CONFIG_SOC_TWAI_SUPPORTED;CONFIG_SOC_GDMA_SUPPORTED;CONFIG_SOC_GPTIMER_SUPPORTED;CONFIG_SOC_LCDCAM_SUPPORTED;CONFIG_SOC_MCPWM_SUPPORTED;CONFIG_SOC_DEDICATED_GPIO_SUPPORTED;CONFIG_SOC_CACHE_SUPPORT_WRAP;CONFIG_SOC_ULP_SUPPORTED;CONFIG_SOC_ULP_FSM_SUPPORTED;CONFIG_SOC_RISCV_COPROC_SUPPORTED;CONFIG_SOC_BT_SUPPORTED;CONFIG_SOC_USB_OTG_SUPPORTED;CONFIG_SOC_USB_SERIAL_JTAG_SUPPORTED;CONFIG_SOC_CCOMP_TIMER_SUPPORTED;CONFIG_SOC_ASYNC_MEMCPY_SUPPORTED;CONFIG_SOC_SUPPORTS_SECURE_DL_MODE;CONFIG_SOC_EFUSE_KEY_PURPOSE_FIELD;CONFIG_SOC_SDMMC_HOST_SUPPORTED;CONFIG_SOC_RTC_FAST_MEM_SUPPORTED;CONFIG_SOC_RTC_SLOW_MEM_SUPPORTED;CONFIG_SOC_RTC_MEM_SUPPORTED;CONFIG_SOC_PSRAM_DMA_CAPABLE;CONFIG_SOC_XT_WDT_SUPPORTED;CONFIG_SOC_I2S_SUPPORTED;CONFIG_SOC_RMT_SUPPORTED;CONFIG_SOC_SDM_SUPPORTED;CONFIG_SOC_GPSPI_SUPPORTED;CONFIG_SOC_LEDC_SUPPORTED;CONFIG_SOC_I2C_SUPPORTED;CONFIG_SOC_SYSTIMER_SUPPORTED;CONFIG_SOC_SUPPORT_COEXISTENCE;CONFIG_SOC_TEMP_SENSOR_SUPPORTED;CONFIG_SOC_AES_SUPPORTED;CONFIG_SOC_MPI_SUPPORTED;CONFIG_SOC_SHA_SUPPORTED;CONFIG_SOC_HMAC_SUPPORTED;CONFIG_SOC_DIG_SIGN_SUPPORTED;CONFIG_SOC_FLASH_ENC_SUPPORTED;CONFIG_SOC_SECURE_BOOT_SUPPORTED;CONFIG_SOC_MEMPROT_SUPPORTED;CONFIG_SOC_TOUCH_SENSOR_SUPPORTED;CONFIG_SOC_BOD_SUPPORTED;CONFIG_SOC_XTAL_SUPPORT_40M;CONFIG_SOC_APPCPU_HAS_CLOCK_GATING_BUG;CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED;CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED;CONFIG_SOC_ADC_ARBITER_SUPPORTED;CONFIG_SOC_ADC_DIG_IIR_FILTER_SUPPORTED;CONFIG_SOC_ADC_MONITOR_SUPPORTED;CONFIG_SOC_ADC_DMA_SUPPORTED;CONFIG_SOC_ADC_PERIPH_NUM;CONFIG_SOC_ADC_MAX_CHANNEL_NUM;CONFIG_SOC_ADC_ATTEN_NUM;CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM;CONFIG_SOC_ADC_PATT_LEN_MAX;CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH;CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH;CONFIG_SOC_ADC_DIGI_RESULT_BYTES;CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV;CONFIG_SOC_ADC_DIGI_IIR_FILTER_NUM;CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH;CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW;CONFIG_SOC_ADC_RTC_MIN_BITWIDTH;CONFIG_SOC_ADC_RTC_MAX_BITWIDTH;CONFIG_SOC_ADC_CALIBRATION_V1_SUPPORTED;CONFIG_SOC_ADC_SELF_HW_CALI_SUPPORTED;CONFIG_SOC_APB_BACKUP_DMA;CONFIG_SOC_BROWNOUT_RESET_SUPPORTED;CONFIG_SOC_CACHE_WRITEBACK_SUPPORTED;CONFIG_SOC_CACHE_FREEZE_SUPPORTED;CONFIG_SOC_CPU_CORES_NUM;CONFIG_SOC_CPU_INTR_NUM;CONFIG_SOC_CPU_HAS_FPU;CONFIG_SOC_CPU_BREAKPOINTS_NUM;CONFIG_SOC_CPU_WATCHPOINTS_NUM;CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE;CONFIG_SOC_DS_SIGNATURE_MAX_BIT_LEN;CONFIG_SOC_DS_KEY_PARAM_MD_IV_LENGTH;CONFIG_SOC_DS_KEY_CHECK_MAX_WAIT_US;CONFIG_SOC_GDMA_GROUPS;CONFIG_SOC_GDMA_PAIRS_PER_GROUP;CONFIG_SOC_GDMA_SUPPORT_PSRAM;CONFIG_SOC_GPIO_PORT;CONFIG_SOC_GPIO_PIN_COUNT;CONFIG_SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER;CONFIG_SOC_GPIO_FILTER_CLK_SUPPORT_APB;CONFIG_SOC_GPIO_SUPPORT_RTC_INDEPENDENT;CONFIG_SOC_GPIO_SUPPORT_FORCE_HOLD;CONFIG_SOC_GPIO_VALID_GPIO_MASK;CONFIG_SOC_GPIO_IN_RANGE_MAX;CONFIG_SOC_GPIO_OUT_RANGE_MAX;CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK;CONFIG_SOC_DEDIC_GPIO_OUT_CHANNELS_NUM;CONFIG_SOC_DEDIC_GPIO_IN_CHANNELS_NUM;CONFIG_SOC_DEDIC_GPIO_OUT_AUTO_ENABLE;CONFIG_SOC_I2C_NUM;CONFIG_SOC_I2C_FIFO_LEN;CONFIG_SOC_I2C_CMD_REG_NUM;CONFIG_SOC_I2C_SUPPORT_SLAVE;CONFIG_SOC_I2C_SUPPORT_HW_CLR_BUS;CONFIG_SOC_I2C_SUPPORT_XTAL;CONFIG_SOC_I2C_SUPPORT_RTC;CONFIG_SOC_I2S_NUM;CONFIG_SOC_I2S_HW_VERSION_2;CONFIG_SOC_I2S_SUPPORTS_XTAL;CONFIG_SOC_I2S_SUPPORTS_PLL_F160M;CONFIG_SOC_I2S_SUPPORTS_PCM;CONFIG_SOC_I2S_SUPPORTS_PDM;CONFIG_SOC_I2S_SUPPORTS_PDM_TX;CONFIG_SOC_I2S_PDM_MAX_TX_LINES;CONFIG_SOC_I2S_SUPPORTS_PDM_RX;CONFIG_SOC_I2S_PDM_MAX_RX_LINES;CONFIG_SOC_I2S_SUPPORTS_TDM;CONFIG_SOC_LEDC_SUPPORT_APB_CLOCK;CONFIG_SOC_LEDC_SUPPORT_XTAL_CLOCK;CONFIG_SOC_LEDC_CHANNEL_NUM;CONFIG_SOC_LEDC_TIMER_BIT_WIDTH;CONFIG_SOC_LEDC_SUPPORT_FADE_STOP;CONFIG_SOC_MCPWM_GROUPS;CONFIG_SOC_MCPWM_TIMERS_PER_GROUP;CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP;CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR;CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR;CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR;CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP;CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP;CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER;CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP;CONFIG_SOC_MCPWM_SWSYNC_CAN_PROPAGATE;CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM;CONFIG_SOC_MMU_PERIPH_NUM;CONFIG_SOC_PCNT_GROUPS;CONFIG_SOC_PCNT_UNITS_PER_GROUP;CONFIG_SOC_PCNT_CHANNELS_PER_UNIT;CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT;CONFIG_SOC_RMT_GROUPS;CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP;CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP;CONFIG_SOC_RMT_CHANNELS_PER_GROUP;CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL;CONFIG_SOC_RMT_SUPPORT_RX_PINGPONG;CONFIG_SOC_RMT_SUPPORT_RX_DEMODULATION;CONFIG_SOC_RMT_SUPPORT_TX_ASYNC_STOP;CONFIG_SOC_RMT_SUPPORT_TX_LOOP_COUNT;CONFIG_SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP;CONFIG_SOC_RMT_SUPPORT_TX_SYNCHRO;CONFIG_SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY;CONFIG_SOC_RMT_SUPPORT_XTAL;CONFIG_SOC_RMT_SUPPORT_RC_FAST;CONFIG_SOC_RMT_SUPPORT_APB;CONFIG_SOC_RMT_SUPPORT_DMA;CONFIG_SOC_LCD_I80_SUPPORTED;CONFIG_SOC_LCD_RGB_SUPPORTED;CONFIG_SOC_LCD_I80_BUSES;CONFIG_SOC_LCD_RGB_PANELS;CONFIG_SOC_LCD_I80_BUS_WIDTH;CONFIG_SOC_LCD_RGB_DATA_WIDTH;CONFIG_SOC_LCD_SUPPORT_RGB_YUV_CONV;CONFIG_SOC_RTC_CNTL_CPU_PD_DMA_BUS_WIDTH;CONFIG_SOC_RTC_CNTL_CPU_PD_REG_FILE_NUM;CONFIG_SOC_RTC_CNTL_TAGMEM_PD_DMA_BUS_WIDTH;CONFIG_SOC_RTCIO_PIN_COUNT;CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED;CONFIG_SOC_RTCIO_HOLD_SUPPORTED;CONFIG_SOC_RTCIO_WAKE_SUPPORTED;CONFIG_SOC_SDM_GROUPS;CONFIG_SOC_SDM_CHANNELS_PER_GROUP;CONFIG_SOC_SDM_CLK_SUPPORT_APB;CONFIG_SOC_SPI_PERIPH_NUM;CONFIG_SOC_SPI_MAX_CS_NUM;CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE;CONFIG_SOC_SPI_SUPPORT_DDRCLK;CONFIG_SOC_SPI_SLAVE_SUPPORT_SEG_TRANS;CONFIG_SOC_SPI_SUPPORT_CD_SIG;CONFIG_SOC_SPI_SUPPORT_CONTINUOUS_TRANS;CONFIG_SOC_SPI_SUPPORT_SLAVE_HD_VER2;CONFIG_SOC_SPI_SUPPORT_CLK_APB;CONFIG_SOC_SPI_SUPPORT_CLK_XTAL;CONFIG_SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT;CONFIG_SOC_MEMSPI_IS_INDEPENDENT;CONFIG_SOC_SPI_MAX_PRE_DIVIDER;CONFIG_SOC_SPI_SUPPORT_OCT;CONFIG_SOC_MEMSPI_SRC_FREQ_120M;CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED;CONFIG_SOC_SPIRAM_SUPPORTED;CONFIG_SOC_SPIRAM_XIP_SUPPORTED;CONFIG_SOC_SYSTIMER_COUNTER_NUM;CONFIG_SOC_SYSTIMER_ALARM_NUM;CONFIG_SOC_SYSTIMER_BIT_WIDTH_LO;CONFIG_SOC_SYSTIMER_BIT_WIDTH_HI;CONFIG_SOC_SYSTIMER_FIXED_DIVIDER;CONFIG_SOC_SYSTIMER_INT_LEVEL;CONFIG_SOC_SYSTIMER_ALARM_MISS_COMPENSATE;CONFIG_SOC_TIMER_GROUPS;CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP;CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH;CONFIG_SOC_TIMER_GROUP_SUPPORT_XTAL;CONFIG_SOC_TIMER_GROUP_SUPPORT_APB;CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS;CONFIG_SOC_TOUCH_VERSION_2;CONFIG_SOC_TOUCH_SENSOR_NUM;CONFIG_SOC_TOUCH_PROXIMITY_CHANNEL_NUM;CONFIG_SOC_TOUCH_PROXIMITY_MEAS_DONE_SUPPORTED;CONFIG_SOC_TOUCH_PAD_THRESHOLD_MAX;CONFIG_SOC_TOUCH_PAD_MEASURE_WAIT_MAX;CONFIG_SOC_TWAI_CONTROLLER_NUM;CONFIG_SOC_TWAI_CLK_SUPPORT_APB;CONFIG_SOC_TWAI_BRP_MIN;CONFIG_SOC_TWAI_BRP_MAX;CONFIG_SOC_TWAI_SUPPORTS_RX_STATUS;CONFIG_SOC_UART_NUM;CONFIG_SOC_UART_FIFO_LEN;CONFIG_SOC_UART_BITRATE_MAX;CONFIG_SOC_UART_SUPPORT_FSM_TX_WAIT_SEND;CONFIG_SOC_UART_SUPPORT_WAKEUP_INT;CONFIG_SOC_UART_SUPPORT_APB_CLK;CONFIG_SOC_UART_SUPPORT_RTC_CLK;CONFIG_SOC_UART_SUPPORT_XTAL_CLK;CONFIG_SOC_UART_REQUIRE_CORE_RESET;CONFIG_SOC_USB_OTG_PERIPH_NUM;CONFIG_SOC_SHA_DMA_MAX_BUFFER_SIZE;CONFIG_SOC_SHA_SUPPORT_DMA;CONFIG_SOC_SHA_SUPPORT_RESUME;CONFIG_SOC_SHA_GDMA;CONFIG_SOC_SHA_SUPPORT_SHA1;CONFIG_SOC_SHA_SUPPORT_SHA224;CONFIG_SOC_SHA_SUPPORT_SHA256;CONFIG_SOC_SHA_SUPPORT_SHA384;CONFIG_SOC_SHA_SUPPORT_SHA512;CONFIG_SOC_SHA_SUPPORT_SHA512_224;CONFIG_SOC_SHA_SUPPORT_SHA512_256;CONFIG_SOC_SHA_SUPPORT_SHA512_T;CONFIG_SOC_RSA_MAX_BIT_LEN;CONFIG_SOC_AES_SUPPORT_DMA;CONFIG_SOC_AES_GDMA;CONFIG_SOC_AES_SUPPORT_AES_128;CONFIG_SOC_AES_SUPPORT_AES_256;CONFIG_SOC_PM_SUPPORT_EXT0_WAKEUP;CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP;CONFIG_SOC_PM_SUPPORT_EXT_WAKEUP;CONFIG_SOC_PM_SUPPORT_WIFI_WAKEUP;CONFIG_SOC_PM_SUPPORT_BT_WAKEUP;CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP;CONFIG_SOC_PM_SUPPORT_CPU_PD;CONFIG_SOC_PM_SUPPORT_TAGMEM_PD;CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD;CONFIG_SOC_PM_SUPPORT_RC_FAST_PD;CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD;CONFIG_SOC_PM_SUPPORT_MAC_BB_PD;CONFIG_SOC_PM_SUPPORT_MODEM_PD;CONFIG_SOC_CONFIGURABLE_VDDSDIO_SUPPORTED;CONFIG_SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY;CONFIG_SOC_PM_CPU_RETENTION_BY_RTCCNTL;CONFIG_SOC_PM_MODEM_RETENTION_BY_BACKUPDMA;CONFIG_SOC_CLK_RC_FAST_D256_SUPPORTED;CONFIG_SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256;CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION;CONFIG_SOC_CLK_XTAL32K_SUPPORTED;CONFIG_SOC_EFUSE_DIS_DOWNLOAD_ICACHE;CONFIG_SOC_EFUSE_DIS_DOWNLOAD_DCACHE;CONFIG_SOC_EFUSE_HARD_DIS_JTAG;CONFIG_SOC_EFUSE_DIS_USB_JTAG;CONFIG_SOC_EFUSE_SOFT_DIS_JTAG;CONFIG_SOC_EFUSE_DIS_DIRECT_BOOT;CONFIG_SOC_EFUSE_DIS_ICACHE;CONFIG_SOC_EFUSE_BLOCK9_KEY_PURPOSE_QUIRK;CONFIG_SOC_SECURE_BOOT_V2_RSA;CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS;CONFIG_SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS;CONFIG_SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY;CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_128;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_256;CONFIG_SOC_MEMPROT_CPU_PREFETCH_PAD_SIZE;CONFIG_SOC_MEMPROT_MEM_ALIGN_SIZE;CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE;CONFIG_SOC_MAC_BB_PD_MEM_SIZE;CONFIG_SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH;CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE;CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND;CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_RESUME;CONFIG_SOC_SPI_MEM_SUPPORT_SW_SUSPEND;CONFIG_SOC_SPI_MEM_SUPPORT_OPI_MODE;CONFIG_SOC_SPI_MEM_SUPPORT_TIME_TUNING;CONFIG_SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE;CONFIG_SOC_SPI_MEM_SUPPORT_WRAP;CONFIG_SOC_COEX_HW_PTI;CONFIG_SOC_EXTERNAL_COEX_LEADER_TX_LINE;CONFIG_SOC_SDMMC_USE_GPIO_MATRIX;CONFIG_SOC_SDMMC_NUM_SLOTS;CONFIG_SOC_SDMMC_SUPPORT_XTAL_CLOCK;CONFIG_SOC_TEMPERATURE_SENSOR_SUPPORT_FAST_RC;CONFIG_SOC_WIFI_HW_TSF;CONFIG_SOC_WIFI_FTM_SUPPORT;CONFIG_SOC_WIFI_GCMP_SUPPORT;CONFIG_SOC_WIFI_WAPI_SUPPORT;CONFIG_SOC_WIFI_CSI_SUPPORT;CONFIG_SOC_WIFI_MESH_SUPPORT;CONFIG_SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW;CONFIG_SOC_WIFI_PHY_NEEDS_USB_WORKAROUND;CONFIG_SOC_BLE_SUPPORTED;CONFIG_SOC_BLE_MESH_SUPPORTED;CONFIG_SOC_BLE_50_SUPPORTED;CONFIG_SOC_BLE_DEVICE_PRIVACY_SUPPORTED;CONFIG_SOC_BLUFI_SUPPORTED;CONFIG_SOC_ULP_HAS_ADC;CONFIG_SOC_PHY_COMBO_MODULE;CONFIG_IDF_CMAKE;CONFIG_IDF_TARGET_ARCH_XTENSA;CONFIG_IDF_TARGET_ARCH;CONFIG_IDF_TARGET;CONFIG_IDF_TARGET_ESP32S3;CONFIG_IDF_FIRMWARE_CHIP_ID;CONFIG_APP_BUILD_TYPE_APP_2NDBOOT;CONFIG_APP_BUILD_TYPE_RAM;CONFIG_APP_BUILD_TYPE_ELF_RAM;CONFIG_APP_BUILD_GENERATE_BINARIES;CONFIG_APP_BUILD_BOOTLOADER;CONFIG_APP_BUILD_USE_FLASH_SECTIONS;CONFIG_APP_REPRODUCIBLE_BUILD;CONFIG_APP_NO_BLOBS;CONFIG_NO_BLOBS;CONFIG_BOOTLOADER_OFFSET_IN_FLASH;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_NONE;CONFIG_BOOTLOADER_LOG_LEVEL_NONE;CONFIG_LOG_BOOTLOADER_LEVEL_NONE;CONFIG_BOOTLOADER_LOG_LEVEL_ERROR;CONFIG_LOG_BOOTLOADER_LEVEL_ERROR;CONFIG_BOOTLOADER_LOG_LEVEL_WARN;CONFIG_LOG_BOOTLOADER_LEVEL_WARN;CONFIG_BOOTLOADER_LOG_LEVEL_INFO;CONFIG_LOG_BOOTLOADER_LEVEL_INFO;CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG;CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG;CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE;CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE;CONFIG_BOOTLOADER_LOG_LEVEL;CONFIG_LOG_BOOTLOADER_LEVEL;CONFIG_BOOTLOADER_FLASH_DC_AWARE;CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT;CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_9V;CONFIG_BOOTLOADER_FACTORY_RESET;CONFIG_BOOTLOADER_APP_TEST;CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE;CONFIG_BOOTLOADER_WDT_ENABLE;CONFIG_BOOTLOADER_WDT_DISABLE_IN_USER_CODE;CONFIG_BOOTLOADER_WDT_TIME_MS;CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE;CONFIG_APP_ROLLBACK_ENABLE;CONFIG_BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP;CONFIG_BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON;CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS;CONFIG_BOOTLOADER_RESERVE_RTC_SIZE;CONFIG_BOOTLOADER_CUSTOM_RESERVE_RTC;CONFIG_SECURE_BOOT_V2_RSA_SUPPORTED;CONFIG_SECURE_BOOT_V2_PREFERRED;CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT;CONFIG_SECURE_BOOT;CONFIG_SECURE_FLASH_ENC_ENABLED;CONFIG_FLASH_ENCRYPTION_ENABLED;CONFIG_SECURE_ROM_DL_MODE_ENABLED;CONFIG_APP_COMPILE_TIME_DATE;CONFIG_APP_EXCLUDE_PROJECT_VER_VAR;CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR;CONFIG_APP_PROJECT_VER_FROM_CONFIG;CONFIG_APP_RETRIEVE_LEN_ELF_SHA;CONFIG_ESP_ROM_HAS_CRC_LE;CONFIG_ESP_ROM_HAS_CRC_BE;CONFIG_ESP_ROM_HAS_MZ_CRC32;CONFIG_ESP_ROM_HAS_JPEG_DECODE;CONFIG_ESP_ROM_UART_CLK_IS_XTAL;CONFIG_ESP_ROM_HAS_RETARGETABLE_LOCKING;CONFIG_ESP_ROM_USB_OTG_NUM;CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM;CONFIG_ESP_ROM_HAS_ERASE_0_REGION_BUG;CONFIG_ESP_ROM_HAS_ENCRYPTED_WRITES_USING_LEGACY_DRV;CONFIG_ESP_ROM_GET_CLK_FREQ;CONFIG_ESP_ROM_HAS_HAL_WDT;CONFIG_ESP_ROM_NEEDS_SWSETUP_WORKAROUND;CONFIG_ESP_ROM_HAS_LAYOUT_TABLE;CONFIG_ESP_ROM_HAS_SPI_FLASH;CONFIG_ESP_ROM_HAS_ETS_PRINTF_BUG;CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT;CONFIG_ESP_ROM_NEEDS_SET_CACHE_MMU_SIZE;CONFIG_ESP_ROM_RAM_APP_NEEDS_MMU_INIT;CONFIG_ESP_ROM_HAS_FLASH_COUNT_PAGES_BUG;CONFIG_ESP_ROM_HAS_CACHE_SUSPEND_WAITI_BUG;CONFIG_ESP_ROM_HAS_CACHE_WRITEBACK_BUG;CONFIG_BOOT_ROM_LOG_ALWAYS_ON;CONFIG_BOOT_ROM_LOG_ALWAYS_OFF;CONFIG_BOOT_ROM_LOG_ON_GPIO_HIGH;CONFIG_BOOT_ROM_LOG_ON_GPIO_LOW;CONFIG_ESPTOOLPY_NO_STUB;CONFIG_ESPTOOLPY_OCT_FLASH;CONFIG_ESPTOOLPY_FLASH_MODE_AUTO_DETECT;CONFIG_ESPTOOLPY_FLASHMODE_QIO;CONFIG_FLASHMODE_QIO;CONFIG_ESPTOOLPY_FLASHMODE_QOUT;CONFIG_FLASHMODE_QOUT;CONFIG_ESPTOOLPY_FLASHMODE_DIO;CONFIG_FLASHMODE_DIO;CONFIG_ESPTOOLPY_FLASHMODE_DOUT;CONFIG_FLASHMODE_DOUT;CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR;CONFIG_ESPTOOLPY_FLASHMODE;CONFIG_ESPTOOLPY_FLASHFREQ_120M;CONFIG_ESPTOOLPY_FLASHFREQ_80M;CONFIG_ESPTOOLPY_FLASHFREQ_40M;CONFIG_ESPTOOLPY_FLASHFREQ_20M;CONFIG_ESPTOOLPY_FLASHFREQ_80M_DEFAULT;CONFIG_ESPTOOLPY_FLASHFREQ;CONFIG_ESPTOOLPY_FLASHSIZE_1MB;CONFIG_ESPTOOLPY_FLASHSIZE_2MB;CONFIG_ESPTOOLPY_FLASHSIZE_4MB;CONFIG_ESPTOOLPY_FLASHSIZE_8MB;CONFIG_ESPTOOLPY_FLASHSIZE_16MB;CONFIG_ESPTOOLPY_FLASHSIZE_32MB;CONFIG_ESPTOOLPY_FLASHSIZE_64MB;CONFIG_ESPTOOLPY_FLASHSIZE_128MB;CONFIG_ESPTOOLPY_FLASHSIZE;CONFIG_ESPTOOLPY_HEADER_FLASHSIZE_UPDATE;CONFIG_ESPTOOLPY_BEFORE_RESET;CONFIG_ESPTOOLPY_BEFORE_NORESET;CONFIG_ESPTOOLPY_BEFORE;CONFIG_ESPTOOLPY_AFTER_RESET;CONFIG_ESPTOOLPY_AFTER_NORESET;CONFIG_ESPTOOLPY_AFTER;CONFIG_ESPTOOLPY_MONITOR_BAUD;CONFIG_MONITOR_BAUD;CONFIG_PARTITION_TABLE_SINGLE_APP;CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE;CONFIG_PARTITION_TABLE_TWO_OTA;CONFIG_PARTITION_TABLE_CUSTOM;CONFIG_PARTITION_TABLE_CUSTOM_FILENAME;CONFIG_PARTITION_TABLE_FILENAME;CONFIG_PARTITION_TABLE_OFFSET;CONFIG_PARTITION_TABLE_MD5;CONFIG_COMPILER_OPTIMIZATION_DEFAULT;CONFIG_OPTIMIZATION_LEVEL_DEBUG;CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG;CONFIG_COMPILER_OPTIMIZATION_SIZE;CONFIG_OPTIMIZATION_LEVEL_RELEASE;CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE;CONFIG_COMPILER_OPTIMIZATION_PERF;CONFIG_COMPILER_OPTIMIZATION_NONE;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE;CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT;CONFIG_OPTIMIZATION_ASSERTIONS_SILENT;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE;CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED;CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB;CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL;CONFIG_OPTIMIZATION_ASSERTION_LEVEL;CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT;CONFIG_COMPILER_HIDE_PATHS_MACROS;CONFIG_COMPILER_CXX_EXCEPTIONS;CONFIG_CXX_EXCEPTIONS;CONFIG_COMPILER_CXX_RTTI;CONFIG_COMPILER_STACK_CHECK_MODE_NONE;CONFIG_STACK_CHECK_NONE;CONFIG_COMPILER_STACK_CHECK_MODE_NORM;CONFIG_STACK_CHECK_NORM;CONFIG_COMPILER_STACK_CHECK_MODE_STRONG;CONFIG_STACK_CHECK_STRONG;CONFIG_COMPILER_STACK_CHECK_MODE_ALL;CONFIG_STACK_CHECK_ALL;CONFIG_COMPILER_WARN_WRITE_STRINGS;CONFIG_WARN_WRITE_STRINGS;CONFIG_COMPILER_DISABLE_GCC12_WARNINGS;CONFIG_COMPILER_DUMP_RTL_FILES;CONFIG_EFUSE_CUSTOM_TABLE;CONFIG_EFUSE_VIRTUAL;CONFIG_EFUSE_MAX_BLK_LEN;CONFIG_ESP_ERR_TO_NAME_LOOKUP;CONFIG_ESP32S3_REV_MIN_0;CONFIG_ESP32S3_REV_MIN_1;CONFIG_ESP32S3_REV_MIN_2;CONFIG_ESP32S3_REV_MIN_FULL;CONFIG_ESP_REV_MIN_FULL;CONFIG_ESP32S3_REV_MAX_FULL;CONFIG_ESP_REV_MAX_FULL;CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_STA;CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_AP;CONFIG_ESP_MAC_ADDR_UNIVERSE_BT;CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH;CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR;CONFIG_ESP32S3_UNIVERSAL_MAC_ADDRESSES_TWO;CONFIG_ESP32S3_UNIVERSAL_MAC_ADDRESSES_FOUR;CONFIG_ESP32S3_UNIVERSAL_MAC_ADDRESSES;CONFIG_ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC;CONFIG_ESP_SLEEP_POWER_DOWN_FLASH;CONFIG_ESP_SYSTEM_PD_FLASH;CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND;CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU;CONFIG_ESP_SLEEP_RTC_BUS_ISO_WORKAROUND;CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND;CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY;CONFIG_ESP32S3_DEEP_SLEEP_WAKEUP_DELAY;CONFIG_ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY;CONFIG_ESP_SLEEP_DEBUG;CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS;CONFIG_ESP_SLEEP_CACHE_SAFE_ASSERTION;CONFIG_RTC_CLK_SRC_INT_RC;CONFIG_ESP32S3_RTC_CLK_SRC_INT_RC;CONFIG_RTC_CLK_SRC_EXT_CRYS;CONFIG_ESP32S3_RTC_CLK_SRC_EXT_CRYS;CONFIG_RTC_CLK_SRC_EXT_OSC;CONFIG_ESP32S3_RTC_CLK_SRC_EXT_OSC;CONFIG_RTC_CLK_SRC_INT_8MD256;CONFIG_ESP32S3_RTC_CLK_SRC_INT_8MD256;CONFIG_RTC_CLK_CAL_CYCLES;CONFIG_ESP32S3_RTC_CLK_CAL_CYCLES;CONFIG_PERIPH_CTRL_FUNC_IN_IRAM;CONFIG_GDMA_CTRL_FUNC_IN_IRAM;CONFIG_GDMA_ISR_IRAM_SAFE;CONFIG_XTAL_FREQ_40;CONFIG_XTAL_FREQ;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80;CONFIG_ESP32S3_DEFAULT_CPU_FREQ_80;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160;CONFIG_ESP32S3_DEFAULT_CPU_FREQ_160;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240;CONFIG_ESP32S3_DEFAULT_CPU_FREQ_240;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ;CONFIG_ESP32S3_DEFAULT_CPU_FREQ_MHZ;CONFIG_ESP32S3_INSTRUCTION_CACHE_16KB;CONFIG_ESP32S3_INSTRUCTION_CACHE_32KB;CONFIG_ESP32S3_INSTRUCTION_CACHE_SIZE;CONFIG_ESP32S3_INSTRUCTION_CACHE_4WAYS;CONFIG_ESP32S3_INSTRUCTION_CACHE_8WAYS;CONFIG_ESP32S3_ICACHE_ASSOCIATED_WAYS;CONFIG_ESP32S3_INSTRUCTION_CACHE_LINE_16B;CONFIG_ESP32S3_INSTRUCTION_CACHE_LINE_32B;CONFIG_ESP32S3_INSTRUCTION_CACHE_LINE_SIZE;CONFIG_ESP32S3_DATA_CACHE_16KB;CONFIG_ESP32S3_DATA_CACHE_32KB;CONFIG_ESP32S3_DATA_CACHE_64KB;CONFIG_ESP32S3_DATA_CACHE_SIZE;CONFIG_ESP32S3_DATA_CACHE_4WAYS;CONFIG_ESP32S3_DATA_CACHE_8WAYS;CONFIG_ESP32S3_DCACHE_ASSOCIATED_WAYS;CONFIG_ESP32S3_DATA_CACHE_LINE_16B;CONFIG_ESP32S3_DATA_CACHE_LINE_32B;CONFIG_ESP32S3_DATA_CACHE_LINE_64B;CONFIG_ESP32S3_DATA_CACHE_LINE_SIZE;CONFIG_ESP32S3_RTCDATA_IN_FAST_MEM;CONFIG_ESP32S3_USE_FIXED_STATIC_RAM_SIZE;CONFIG_ESP32S3_TRAX;CONFIG_ESP32S3_TRACEMEM_RESERVE_DRAM;CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT;CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT;CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT;CONFIG_ESP_SYSTEM_PANIC_GDBSTUB;CONFIG_ESP_SYSTEM_GDBSTUB_RUNTIME;CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS;CONFIG_ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK;CONFIG_ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP;CONFIG_ESP_SYSTEM_MEMPROT_FEATURE;CONFIG_ESP_SYSTEM_MEMPROT_FEATURE_LOCK;CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE;CONFIG_SYSTEM_EVENT_QUEUE_SIZE;CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE;CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE;CONFIG_ESP_MAIN_TASK_STACK_SIZE;CONFIG_MAIN_TASK_STACK_SIZE;CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0;CONFIG_ESP_MAIN_TASK_AFFINITY_CPU1;CONFIG_ESP_MAIN_TASK_AFFINITY_NO_AFFINITY;CONFIG_ESP_MAIN_TASK_AFFINITY;CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE;CONFIG_ESP_CONSOLE_UART_DEFAULT;CONFIG_CONSOLE_UART_DEFAULT;CONFIG_ESP_CONSOLE_USB_CDC;CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG;CONFIG_ESP_CONSOLE_UART_CUSTOM;CONFIG_CONSOLE_UART_CUSTOM;CONFIG_ESP_CONSOLE_NONE;CONFIG_CONSOLE_UART_NONE;CONFIG_ESP_CONSOLE_UART_NONE;CONFIG_ESP_CONSOLE_SECONDARY_NONE;CONFIG_ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG;CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED;CONFIG_ESP_CONSOLE_UART;CONFIG_CONSOLE_UART;CONFIG_ESP_CONSOLE_MULTIPLE_UART;CONFIG_ESP_CONSOLE_UART_NUM;CONFIG_CONSOLE_UART_NUM;CONFIG_ESP_CONSOLE_UART_BAUDRATE;CONFIG_CONSOLE_UART_BAUDRATE;CONFIG_ESP_INT_WDT;CONFIG_INT_WDT;CONFIG_ESP_INT_WDT_TIMEOUT_MS;CONFIG_INT_WDT_TIMEOUT_MS;CONFIG_ESP_INT_WDT_CHECK_CPU1;CONFIG_INT_WDT_CHECK_CPU1;CONFIG_ESP_TASK_WDT_EN;CONFIG_ESP_TASK_WDT_INIT;CONFIG_TASK_WDT;CONFIG_ESP_TASK_WDT;CONFIG_ESP_TASK_WDT_PANIC;CONFIG_TASK_WDT_PANIC;CONFIG_ESP_TASK_WDT_TIMEOUT_S;CONFIG_TASK_WDT_TIMEOUT_S;CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0;CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0;CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1;CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1;CONFIG_ESP_PANIC_HANDLER_IRAM;CONFIG_ESP_DEBUG_STUBS_ENABLE;CONFIG_ESP32_DEBUG_STUBS_ENABLE;CONFIG_ESP_DEBUG_OCDAWARE;CONFIG_ESP32S3_DEBUG_OCDAWARE;CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4;CONFIG_ESP_BROWNOUT_DET;CONFIG_BROWNOUT_DET;CONFIG_ESP32S3_BROWNOUT_DET;CONFIG_ESP32S3_BROWNOUT_DET;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7;CONFIG_BROWNOUT_DET_LVL_SEL_7;CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_7;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6;CONFIG_BROWNOUT_DET_LVL_SEL_6;CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_6;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5;CONFIG_BROWNOUT_DET_LVL_SEL_5;CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_5;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_4;CONFIG_BROWNOUT_DET_LVL_SEL_4;CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_4;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_3;CONFIG_BROWNOUT_DET_LVL_SEL_3;CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_3;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_2;CONFIG_BROWNOUT_DET_LVL_SEL_2;CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_2;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_1;CONFIG_BROWNOUT_DET_LVL_SEL_1;CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_1;CONFIG_ESP_BROWNOUT_DET_LVL;CONFIG_BROWNOUT_DET_LVL;CONFIG_ESP32S3_BROWNOUT_DET_LVL;CONFIG_ESP_SYSTEM_BROWNOUT_INTR;CONFIG_ESP_SYSTEM_BBPLL_RECALIB;CONFIG_ESP_IPC_TASK_STACK_SIZE;CONFIG_IPC_TASK_STACK_SIZE;CONFIG_ESP_IPC_USES_CALLERS_PRIORITY;CONFIG_ESP_IPC_ISR_ENABLE;CONFIG_FREERTOS_SMP;CONFIG_FREERTOS_UNICORE;CONFIG_FREERTOS_HZ;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY;CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS;CONFIG_FREERTOS_IDLE_TASK_STACKSIZE;CONFIG_FREERTOS_USE_IDLE_HOOK;CONFIG_FREERTOS_USE_TICK_HOOK;CONFIG_FREERTOS_MAX_TASK_NAME_LEN;CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY;CONFIG_FREERTOS_TIMER_TASK_PRIORITY;CONFIG_TIMER_TASK_PRIORITY;CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH;CONFIG_TIMER_TASK_STACK_DEPTH;CONFIG_FREERTOS_TIMER_QUEUE_LENGTH;CONFIG_TIMER_QUEUE_LENGTH;CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE;CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES;CONFIG_FREERTOS_USE_TRACE_FACILITY;CONFIG_FREERTOS_USE_STATS_FORMATTING_FUNCTIONS;CONFIG_FREERTOS_VTASKLIST_INCLUDE_COREID;CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS;CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER;CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK;CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS;CONFIG_FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP;CONFIG_ENABLE_STATIC_TASK_CLEAN_UP_HOOK;CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER;CONFIG_FREERTOS_ISR_STACKSIZE;CONFIG_FREERTOS_INTERRUPT_BACKTRACE;CONFIG_FREERTOS_TICK_SUPPORT_SYSTIMER;CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL1;CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL3;CONFIG_FREERTOS_SYSTICK_USES_SYSTIMER;CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH;CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH;CONFIG_FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE;CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT;CONFIG_FREERTOS_NO_AFFINITY;CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION;CONFIG_FREERTOS_DEBUG_OCDAWARE;CONFIG_HAL_ASSERTION_EQUALS_SYSTEM;CONFIG_HAL_ASSERTION_DISABLE;CONFIG_HAL_ASSERTION_SILENT;CONFIG_HAL_ASSERTION_SILIENT;CONFIG_HAL_ASSERTION_ENABLE;CONFIG_HAL_DEFAULT_ASSERTION_LEVEL;CONFIG_HAL_WDT_USE_ROM_IMPL;CONFIG_LOG_DEFAULT_LEVEL_NONE;CONFIG_LOG_DEFAULT_LEVEL_ERROR;CONFIG_LOG_DEFAULT_LEVEL_WARN;CONFIG_LOG_DEFAULT_LEVEL_INFO;CONFIG_LOG_DEFAULT_LEVEL_DEBUG;CONFIG_LOG_DEFAULT_LEVEL_VERBOSE;CONFIG_LOG_DEFAULT_LEVEL;CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT;CONFIG_LOG_MAXIMUM_LEVEL_DEBUG;CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE;CONFIG_LOG_MAXIMUM_LEVEL;CONFIG_LOG_COLORS;CONFIG_LOG_TIMESTAMP_SOURCE_RTOS;CONFIG_LOG_TIMESTAMP_SOURCE_SYSTEM;CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF;CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF;CONFIG_NEWLIB_STDOUT_LINE_ENDING_CR;CONFIG_NEWLIB_STDIN_LINE_ENDING_CRLF;CONFIG_NEWLIB_STDIN_LINE_ENDING_LF;CONFIG_NEWLIB_STDIN_LINE_ENDING_CR;CONFIG_NEWLIB_NANO_FORMAT;CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT;CONFIG_ESP32S3_TIME_SYSCALL_USE_RTC_SYSTIMER;CONFIG_ESP32S3_TIME_SYSCALL_USE_RTC_FRC1;CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC;CONFIG_ESP32S3_TIME_SYSCALL_USE_RTC;CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT;CONFIG_ESP32S3_TIME_SYSCALL_USE_SYSTIMER;CONFIG_ESP32S3_TIME_SYSCALL_USE_FRC1;CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE;CONFIG_ESP32S3_TIME_SYSCALL_USE_NONE;CONFIG_MMU_PAGE_SIZE_64KB;CONFIG_MMU_PAGE_MODE;CONFIG_MMU_PAGE_SIZE;CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC;CONFIG_SPI_FLASH_BROWNOUT_RESET;CONFIG_SPI_FLASH_HPM_ENA;CONFIG_SPI_FLASH_HPM_AUTO;CONFIG_SPI_FLASH_HPM_DIS;CONFIG_SPI_FLASH_HPM_ON;CONFIG_SPI_FLASH_HPM_DC_AUTO;CONFIG_SPI_FLASH_HPM_DC_DISABLE;CONFIG_SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND;CONFIG_SPI_FLASH_VERIFY_WRITE;CONFIG_SPI_FLASH_ENABLE_COUNTERS;CONFIG_SPI_FLASH_ROM_DRIVER_PATCH;CONFIG_SPI_FLASH_ROM_IMPL;CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS;CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_FAILS;CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ALLOWED;CONFIG_SPI_FLASH_BYPASS_BLOCK_ERASE;CONFIG_SPI_FLASH_YIELD_DURING_ERASE;CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS;CONFIG_SPI_FLASH_ERASE_YIELD_TICKS;CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE;CONFIG_SPI_FLASH_SIZE_OVERRIDE;CONFIG_SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED;CONFIG_SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST;CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_GD_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_ISSI_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_MXIC_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_WINBOND_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_BOYA_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_TH_SUPPORTED;CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP;CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP;CONFIG_SPI_FLASH_SUPPORT_GD_CHIP;CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP;CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP;CONFIG_SPI_FLASH_SUPPORT_TH_CHIP;CONFIG_SPI_FLASH_SUPPORT_MXIC_OPI_CHIP;CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE;CONFIG_IDF_EXPERIMENTAL_FEATURES)
# List of deprecated options for backward compatibility
set(CONFIG_APP_BUILD_TYPE_ELF_RAM "")
set(CONFIG_NO_BLOBS "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_NONE "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_ERROR "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_WARN "y")
set(CONFIG_LOG_BOOTLOADER_LEVEL_INFO "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE "")
set(CONFIG_LOG_BOOTLOADER_LEVEL "2")
set(CONFIG_APP_ROLLBACK_ENABLE "")
set(CONFIG_FLASH_ENCRYPTION_ENABLED "")
set(CONFIG_FLASHMODE_QIO "")
set(CONFIG_FLASHMODE_QOUT "")
set(CONFIG_FLASHMODE_DIO "y")
set(CONFIG_FLASHMODE_DOUT "")
set(CONFIG_MONITOR_BAUD "115200")
set(CONFIG_OPTIMIZATION_LEVEL_DEBUG "y")
set(CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG "y")
set(CONFIG_OPTIMIZATION_LEVEL_RELEASE "")
set(CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE "")
set(CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED "y")
set(CONFIG_OPTIMIZATION_ASSERTIONS_SILENT "")
set(CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED "")
set(CONFIG_OPTIMIZATION_ASSERTION_LEVEL "2")
set(CONFIG_CXX_EXCEPTIONS "")
set(CONFIG_STACK_CHECK_NONE "y")
set(CONFIG_STACK_CHECK_NORM "")
set(CONFIG_STACK_CHECK_STRONG "")
set(CONFIG_STACK_CHECK_ALL "")
set(CONFIG_WARN_WRITE_STRINGS "")
set(CONFIG_ESP_SYSTEM_PD_FLASH "")
set(CONFIG_ESP32S3_DEEP_SLEEP_WAKEUP_DELAY "2000")
set(CONFIG_ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY "2000")
set(CONFIG_ESP32S3_RTC_CLK_SRC_INT_RC "y")
set(CONFIG_ESP32S3_RTC_CLK_SRC_EXT_CRYS "")
set(CONFIG_ESP32S3_RTC_CLK_SRC_EXT_OSC "")
set(CONFIG_ESP32S3_RTC_CLK_SRC_INT_8MD256 "")
set(CONFIG_ESP32S3_RTC_CLK_CAL_CYCLES "1024")
set(CONFIG_ESP32S3_DEFAULT_CPU_FREQ_80 "")
set(CONFIG_ESP32S3_DEFAULT_CPU_FREQ_160 "y")
set(CONFIG_ESP32S3_DEFAULT_CPU_FREQ_240 "")
set(CONFIG_ESP32S3_DEFAULT_CPU_FREQ_MHZ "160")
set(CONFIG_SYSTEM_EVENT_QUEUE_SIZE "32")
set(CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE "2304")
set(CONFIG_MAIN_TASK_STACK_SIZE "7168")
set(CONFIG_CONSOLE_UART_DEFAULT "y")
set(CONFIG_CONSOLE_UART_CUSTOM "")
set(CONFIG_CONSOLE_UART_NONE "")
set(CONFIG_ESP_CONSOLE_UART_NONE "")
set(CONFIG_CONSOLE_UART "y")
set(CONFIG_CONSOLE_UART_NUM "0")
set(CONFIG_CONSOLE_UART_BAUDRATE "115200")
set(CONFIG_INT_WDT "y")
set(CONFIG_INT_WDT_TIMEOUT_MS "300")
set(CONFIG_INT_WDT_CHECK_CPU1 "y")
set(CONFIG_TASK_WDT "y")
set(CONFIG_ESP_TASK_WDT "y")
set(CONFIG_TASK_WDT_PANIC "")
set(CONFIG_TASK_WDT_TIMEOUT_S "5")
set(CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0 "y")
set(CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1 "y")
set(CONFIG_ESP32_DEBUG_STUBS_ENABLE "")
set(CONFIG_ESP32S3_DEBUG_OCDAWARE "y")
set(CONFIG_BROWNOUT_DET "y")
set(CONFIG_ESP32S3_BROWNOUT_DET "y")
set(CONFIG_ESP32S3_BROWNOUT_DET "y")
set(CONFIG_BROWNOUT_DET_LVL_SEL_7 "y")
set(CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_7 "y")
set(CONFIG_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_4 "")
set(CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_4 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_3 "")
set(CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_3 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_2 "")
set(CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_2 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_1 "")
set(CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_1 "")
set(CONFIG_BROWNOUT_DET_LVL "7")
set(CONFIG_ESP32S3_BROWNOUT_DET_LVL "7")
set(CONFIG_IPC_TASK_STACK_SIZE "1280")
set(CONFIG_TIMER_TASK_PRIORITY "1")
set(CONFIG_TIMER_TASK_STACK_DEPTH "2048")
set(CONFIG_TIMER_QUEUE_LENGTH "10")
set(CONFIG_ENABLE_STATIC_TASK_CLEAN_UP_HOOK "")
set(CONFIG_HAL_ASSERTION_SILIENT "")
set(CONFIG_ESP32S3_TIME_SYSCALL_USE_RTC_SYSTIMER "y")
set(CONFIG_ESP32S3_TIME_SYSCALL_USE_RTC_FRC1 "y")
set(CONFIG_ESP32S3_TIME_SYSCALL_USE_RTC "")
set(CONFIG_ESP32S3_TIME_SYSCALL_USE_SYSTIMER "")
set(CONFIG_ESP32S3_TIME_SYSCALL_USE_FRC1 "")
set(CONFIG_ESP32S3_TIME_SYSCALL_USE_NONE "")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS "y")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_FAILS "")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ALLOWED "")
