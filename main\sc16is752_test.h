#pragma once

#include "driver/i2c.h"

#ifdef __cplusplus
extern "C" {
#endif

// SC16IS752 register definitions
#define SC16IS752_ADDR                 0x4D    // I2C address of SC16IS752
#define SC16IS752_REG_THR              0x00    // Transmit Holding Register (write)
#define SC16IS752_REG_RHR              0x00    // Receive Holding Register (read)
#define SC16IS752_REG_IER              0x01    // Interrupt Enable Register
#define SC16IS752_REG_FCR              0x02    // FIFO Control Register
#define SC16IS752_REG_LCR              0x03    // Line Control Register
#define SC16IS752_REG_MCR              0x04    // Modem Control Register
#define SC16IS752_REG_LSR              0x05    // Line Status Register
#define SC16IS752_REG_MSR              0x06    // Modem Status Register
#define SC16IS752_REG_SPR              0x07    // Scratch Pad Register
#define SC16IS752_REG_DLL              0x00    // Divisor Latch LSB (LCR[7] = 1)
#define SC16IS752_REG_DLH              0x01    // Divisor Latch MSB (LCR[7] = 1)

// Channel selection
#define SC16IS752_CHANNEL_A            0x00
#define SC16IS752_CHANNEL_B            0x01

// Function prototypes
esp_err_t sc16is752_init(i2c_port_t i2c_port);
esp_err_t sc16is752_send_data(i2c_port_t i2c_port, uint8_t channel, const char* data, size_t len);
esp_err_t sc16is752_receive_data(i2c_port_t i2c_port, uint8_t channel, char* data, size_t max_len, size_t* received_len);
void register_sc16is752_cmd(void);

#ifdef __cplusplus
}
#endif
