#include "sc16is752_test.h"
#include "esp_log.h"
#include "esp_console.h"
#include "argtable3/argtable3.h"
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/i2c.h"

// Define buffer size constants if not already defined
#define I2C_MASTER_TX_BUF_DISABLE 0
#define I2C_MASTER_RX_BUF_DISABLE 0

// Your hex data
uint8_t data[] = {0xEF, 0xAA, 0xC1, 0x00, 0x00, 0x00, 0x01, 0x00, 0xC2};
uint8_t data_on[] = {0xEF, 0xAA, 0xC1, 0x00, 0x00, 0x00, 0x01, 0x01, 0xC3};
uint8_t data_off[] = {0xEF, 0xAA, 0xC1, 0x00, 0x00, 0x00, 0x01, 0x00, 0xC2};

static const char *TAG = "sc16is752";

// Store the I2C port number locally
static i2c_port_t s_i2c_port = I2C_NUM_0;

// Helper function to read a register
static esp_err_t sc16is752_read_reg(i2c_port_t i2c_port, uint8_t channel, uint8_t reg, uint8_t *data)
{
    uint8_t reg_addr = (reg << 3) | (channel << 1);
    esp_err_t ret;
    
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (SC16IS752_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg_addr, true);
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (SC16IS752_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read_byte(cmd, data, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(i2c_port, cmd, 1000 / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    
    return ret;
}

// Helper function to write a register
static esp_err_t sc16is752_write_reg(i2c_port_t i2c_port, uint8_t channel, uint8_t reg, uint8_t data)
{
    uint8_t reg_addr = (reg << 3) | (channel << 1);
    esp_err_t ret;
    
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (SC16IS752_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg_addr, true);
    i2c_master_write_byte(cmd, data, true);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(i2c_port, cmd, 1000 / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    
    return ret;
}

esp_err_t sc16is752_init(i2c_port_t i2c_port)
{
    esp_err_t ret;
    
    // Reset FIFOs
    ret = sc16is752_write_reg(i2c_port, SC16IS752_CHANNEL_B, SC16IS752_REG_FCR, 0x06);
    if (ret != ESP_OK) return ret;
    
    // Set baud rate - 9600 bps (assuming 1.8432MHz crystal)
    // First set LCR[7] = 1 to access divisor latches
    ret = sc16is752_write_reg(i2c_port, SC16IS752_CHANNEL_B, SC16IS752_REG_LCR, 0x80);
    if (ret != ESP_OK) return ret;
    
    // Set divisor to 12 (1.8432MHz / 12 / 16 = 9600 bps)
    ret = sc16is752_write_reg(i2c_port, SC16IS752_CHANNEL_B, SC16IS752_REG_DLL, 12);
    if (ret != ESP_OK) return ret;
    ret = sc16is752_write_reg(i2c_port, SC16IS752_CHANNEL_B, SC16IS752_REG_DLH, 0);
    if (ret != ESP_OK) return ret;
    
    // Set 8N1 mode (8 bits, no parity, 1 stop bit)
    ret = sc16is752_write_reg(i2c_port, SC16IS752_CHANNEL_B, SC16IS752_REG_LCR, 0x03);
    if (ret != ESP_OK) return ret;
    
    // Enable FIFOs
    ret = sc16is752_write_reg(i2c_port, SC16IS752_CHANNEL_B, SC16IS752_REG_FCR, 0x01);
    if (ret != ESP_OK) return ret;
    
    // Test the connection by writing to and reading from the scratch register
    ret = sc16is752_write_reg(i2c_port, SC16IS752_CHANNEL_B, SC16IS752_REG_SPR, 0x55);
    if (ret != ESP_OK) return ret;
    
    uint8_t test_val;
    ret = sc16is752_read_reg(i2c_port, SC16IS752_CHANNEL_B, SC16IS752_REG_SPR, &test_val);
    if (ret != ESP_OK) return ret;
    
    if (test_val != 0x55) {
        ESP_LOGE(TAG, "SC16IS752 test failed: wrote 0x55, read 0x%02x", test_val);
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "SC16IS752 initialized successfully");
    return ESP_OK;
}

esp_err_t sc16is752_send_data(i2c_port_t i2c_port, uint8_t channel, const char* data, size_t len)
{
    esp_err_t ret;
    
    for (size_t i = 0; i < len; i++) {
        // Check if THR is empty
        uint8_t lsr;
        ret = sc16is752_read_reg(i2c_port, channel, SC16IS752_REG_LSR, &lsr);
        if (ret != ESP_OK) return ret;
        
        if (!(lsr & 0x20)) {  // THR not empty
            vTaskDelay(1);    // Wait a bit
            i--;              // Retry this byte
            continue;
        }
        
        ret = sc16is752_write_reg(i2c_port, channel, SC16IS752_REG_THR, data[i]);
        if (ret != ESP_OK) return ret;
    }
    
    return ESP_OK;
}

esp_err_t sc16is752_receive_data(i2c_port_t i2c_port, uint8_t channel, char* data, size_t max_len, size_t* received_len)
{
    esp_err_t ret;
    *received_len = 0;
    
    for (size_t i = 0; i < max_len; i++) {
        // Check if data is available
        uint8_t lsr;
        ret = sc16is752_read_reg(i2c_port, channel, SC16IS752_REG_LSR, &lsr);
        if (ret != ESP_OK) return ret;
        
        if (!(lsr & 0x01)) {  // No data available
            break;
        }
        
        uint8_t rx_data;
        ret = sc16is752_read_reg(i2c_port, channel, SC16IS752_REG_RHR, &rx_data);
        if (ret != ESP_OK) return ret;
        
        data[i] = rx_data;
        (*received_len)++;
    }
    
    return ESP_OK;
}

// Command to set I2C port for SC16IS752
static struct {
    struct arg_int *port;
    struct arg_end *end;
} sc16is752_config_args;

static int do_sc16is752_config_cmd(int argc, char **argv)
{
    int nerrors = arg_parse(argc, argv, (void **)&sc16is752_config_args);
    if (nerrors != 0) {
        arg_print_errors(stderr, sc16is752_config_args.end, argv[0]);
        return 1;
    }
    
    if (sc16is752_config_args.port->count > 0) {
        int port = sc16is752_config_args.port->ival[0];
        if (port < 0 || port >= I2C_NUM_MAX) {
            ESP_LOGE(TAG, "Invalid I2C port number");
            return 1;
        }
        s_i2c_port = (i2c_port_t)port;
        ESP_LOGI(TAG, "SC16IS752 I2C port set to %d", s_i2c_port);
    } else {
        ESP_LOGI(TAG, "Current SC16IS752 I2C port: %d", s_i2c_port);
    }
    
    return 0;
}

// Command handlers
static struct {
    struct arg_str *text;
    struct arg_end *end;
} uart_send_args;

static int do_uart_send_cmd(int argc, char **argv)
{
    int nerrors = arg_parse(argc, argv, (void **)&uart_send_args);
    if (nerrors != 0) {
        arg_print_errors(stderr, uart_send_args.end, argv[0]);
        return 1;
    }
    
    if (uart_send_args.text->count == 0) {
        ESP_LOGE(TAG, "No text provided");
        return 1;
    }
    
    const char *text = uart_send_args.text->sval[0];
    esp_err_t ret = sc16is752_send_data(s_i2c_port, SC16IS752_CHANNEL_B, text, strlen(text));
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to send data: %s", esp_err_to_name(ret));
        return 1;
    }
    
    ESP_LOGI(TAG, "Data sent successfully");
    return 0;
}

static int do_uart_receive_cmd(int argc, char **argv)
{
    char buffer[256];
    size_t received_len = 0;
    
    esp_err_t ret = sc16is752_receive_data(s_i2c_port, SC16IS752_CHANNEL_B, buffer, sizeof(buffer) - 1, &received_len);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to receive data: %s", esp_err_to_name(ret));
        return 1;
    }
    
    if (received_len > 0) {
        buffer[received_len] = '\0';
        printf("Received %d bytes: %s\n", received_len, buffer);
        
        // Print hex values
        printf("Hex: ");
        for (size_t i = 0; i < received_len; i++) {
            printf("%02X ", (uint8_t)buffer[i]);
        }
        printf("\n");
    } else {
        printf("No data received\n");
    }
    
    return 0;
}

static int do_uart_test_cmd(int argc, char **argv)
{
    // Default GPIO pins for I2C
    const int i2c_master_sda = 8;  // Default SDA pin
    const int i2c_master_scl = 9;  // Default SCL pin
    const int i2c_master_freq_hz = 100000;  // Default 100KHz
    
    // We'll use our own I2C configuration
    i2c_config_t conf = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = i2c_master_sda,
        .scl_io_num = i2c_master_scl,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = i2c_master_freq_hz,
    };
    
    ESP_LOGI(TAG, "Configuring I2C on port %d (SDA: %d, SCL: %d, Freq: %d Hz)",
             s_i2c_port, i2c_master_sda, i2c_master_scl, i2c_master_freq_hz);
    
    // Install I2C driver
    esp_err_t ret = i2c_param_config(s_i2c_port, &conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C parameter configuration failed: %s", esp_err_to_name(ret));
        return 1;
    }
    
    ret = i2c_driver_install(s_i2c_port, I2C_MODE_MASTER, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C driver installation failed: %s", esp_err_to_name(ret));
        return 1;
    }
    
    ret = sc16is752_init(s_i2c_port);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SC16IS752 initialization failed: %s", esp_err_to_name(ret));
        i2c_driver_delete(s_i2c_port);
        return 1;
    }
    
    // Send a test message
    const char *test_msg = "SC16IS752 Test\r\n";

    ret = sc16is752_send_data(s_i2c_port, SC16IS752_CHANNEL_B, test_msg, strlen(test_msg));
    
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to send test message: %s", esp_err_to_name(ret));
        i2c_driver_delete(s_i2c_port);
        return 1;
    }
    
    ESP_LOGI(TAG, "Test message sent, waiting for response...");
    
    // Wait a bit for response
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // Try to receive response
    char buffer[256];
    size_t received_len = 0;
    
    ret = sc16is752_receive_data(s_i2c_port, SC16IS752_CHANNEL_B, buffer, sizeof(buffer) - 1, &received_len);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to receive response: %s", esp_err_to_name(ret));
        i2c_driver_delete(s_i2c_port);
        return 1;
    }
    
    if (received_len > 0) {
        buffer[received_len] = '\0';
        printf("Received response (%d bytes): %s\n", received_len, buffer);
        
        // Print hex values
        printf("Hex: ");
        for (size_t i = 0; i < received_len; i++) {
            printf("%02X ", (uint8_t)buffer[i]);
        }
        printf("\n");
    } else {
        printf("No response received\n");
    }
    
    i2c_driver_delete(s_i2c_port);
    return 0;
}

void register_sc16is752_cmd(void)
{
    sc16is752_config_args.port = arg_int0("p", "port", "<port>", "I2C port number (0 or 1)");
    sc16is752_config_args.end = arg_end(2);
    
    uart_send_args.text = arg_str1(NULL, NULL, "<text>", "Text to send");
    uart_send_args.end = arg_end(2);
    
    const esp_console_cmd_t sc16is752_config_cmd = {
        .command = "sc16is752_config",
        .help = "Configure SC16IS752 I2C port",
        .hint = NULL,
        .func = &do_sc16is752_config_cmd,
        .argtable = &sc16is752_config_args
    };
    
    const esp_console_cmd_t uart_send_cmd = {
        .command = "uart_send",
        .help = "Send data through SC16IS752 UART",
        .hint = NULL,
        .func = &do_uart_send_cmd,
        .argtable = &uart_send_args
    };
    
    const esp_console_cmd_t uart_receive_cmd = {
        .command = "uart_receive",
        .help = "Receive data from SC16IS752 UART",
        .hint = NULL,
        .func = &do_uart_receive_cmd,
        .argtable = NULL
    };
    
    const esp_console_cmd_t uart_test_cmd = {
        .command = "uart_test",
        .help = "Test SC16IS752 UART connection",
        .hint = NULL,
        .func = &do_uart_test_cmd,
        .argtable = NULL
    };
    
    ESP_ERROR_CHECK(esp_console_cmd_register(&sc16is752_config_cmd));
    ESP_ERROR_CHECK(esp_console_cmd_register(&uart_send_cmd));
    ESP_ERROR_CHECK(esp_console_cmd_register(&uart_receive_cmd));
    ESP_ERROR_CHECK(esp_console_cmd_register(&uart_test_cmd));
}




