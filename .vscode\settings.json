{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPathWin": "c:\\Users\\<USER>\\esp\\v5.1.6\\esp-idf", "idf.toolsPathWin": "c:\\Users\\<USER>\\EspressIF\\tools", "idf.pythonInstallPath": "c:\\Users\\<USER>\\EspressIF\\tools\\tools\\idf-python\\3.11.2\\python.exe", "idf.portWin": "COM5", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.customExtraVars": {"OPENOCD_SCRIPTS": "d:\\vscode\\vsc-lvgl\\tools\\openocd-esp32\\v0.12.0-esp32-20230921/openocd-esp32/share/openocd/scripts", "IDF_CCACHE_ENABLE": "1", "ESP_ROM_ELF_DIR": "d:\\vscode\\vsc-lvgl\\tools\\esp-rom-elfs\\20220823/", "IDF_TARGET": "esp32s3"}, "idf.flashType": "UART"}